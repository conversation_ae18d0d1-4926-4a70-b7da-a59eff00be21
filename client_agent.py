"""Client Agent - Routes requests to appropriate agents using ACPCallingAgent"""

import asyncio
from acp_sdk.client import Client
from fastacp import AgentCollection, ACPCallingAgent
from openrouter_model import OpenRouterModel
from colorama import Fore
from fastapi import FastAPI
import uvicorn

app = FastAPI()

# Initialize OpenRouter model
model = OpenRouterModel()

@app.get("/")
async def root():
    return {
        "message": "Multi-Agent System Client", 
        "description": "Routes requests to Task Generation and Scheduling agents",
        "endpoints": {
            "process_full": "/process-full",
            "process_partial": "/process-partial", 
            "health": "/health"
        }
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "client_agent"}

@app.post("/process-full")
async def process_full_request(request: dict):
    """Process full request: Task Generation + Scheduling"""
    
    project_description = request.get("project_description", "")
    
    print(f"{Fore.CYAN}[CLIENT] Processing FULL request: {project_description}{Fore.RESET}")
    
    try:
        # Connect to agents
        async with <PERSON><PERSON>(base_url="http://localhost:8001") as task_client, \
                   Client(base_url="http://localhost:8002") as schedule_client:
            
            # Get agent collection
            agent_collection = await AgentCollection.from_acp(task_client, schedule_client)
            acp_agents = {agent.name: {'agent': agent, 'client': client} 
                         for client, agent in agent_collection.agents}
            
            print(f"{Fore.GREEN}[CLIENT] Found agents: {list(acp_agents.keys())}{Fore.RESET}")
            
            # Create ACPCallingAgent
            acpagent = ACPCallingAgent(acp_agents=acp_agents, model=model)
            
            # Process request
            query = f"""I need both task generation and scheduling for this project: {project_description}

Please:
1. First call the task_agent to generate a comprehensive task list
2. Then call the scheduling_agent to create a timeline from those tasks
3. Provide a final summary with both the tasks and schedule"""

            result = await acpagent.run(query)
            
            print(f"{Fore.YELLOW}[CLIENT] Final result: {result}{Fore.RESET}")
            
            return {
                "success": True,
                "request_type": "full",
                "project_description": project_description,
                "result": result
            }
            
    except Exception as e:
        print(f"{Fore.RED}[CLIENT] Error: {str(e)}{Fore.RESET}")
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/process-partial")
async def process_partial_request(request: dict):
    """Process partial request: Task Generation only"""
    
    project_description = request.get("project_description", "")
    
    print(f"{Fore.CYAN}[CLIENT] Processing PARTIAL request: {project_description}{Fore.RESET}")
    
    try:
        # Connect to task generation agent only
        async with Client(base_url="http://localhost:8001") as task_client:
            
            # Get agent collection
            agent_collection = await AgentCollection.from_acp(task_client)
            acp_agents = {agent.name: {'agent': agent, 'client': client} 
                         for client, agent in agent_collection.agents}
            
            print(f"{Fore.GREEN}[CLIENT] Found agents: {list(acp_agents.keys())}{Fore.RESET}")
            
            # Create ACPCallingAgent
            acpagent = ACPCallingAgent(acp_agents=acp_agents, model=model)
            
            # Process request
            query = f"""I need task generation for this project: {project_description}

Please call the task_agent to generate a comprehensive task list and provide the results."""

            result = await acpagent.run(query)
            
            print(f"{Fore.YELLOW}[CLIENT] Final result: {result}{Fore.RESET}")
            
            return {
                "success": True,
                "request_type": "partial",
                "project_description": project_description,
                "result": result
            }
            
    except Exception as e:
        print(f"{Fore.RED}[CLIENT] Error: {str(e)}{Fore.RESET}")
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    print("Starting Client Agent on port 8000...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
