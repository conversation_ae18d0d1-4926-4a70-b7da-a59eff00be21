"""Client Router Agent using FastAPI and ACPCallingAgent."""

import asyncio
from typing import Dict, Any, List
from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from loguru import logger
import uvicorn

from acp.calling_agent import ACPCallingAgent
from acp.message_types import ProjectRequest
from config import settings, REQUEST_TYPES
from logging_config import HTTPLoggingMiddleware, setup_agent_logging

# Request/Response models
class ProjectRequestModel(BaseModel):
    """Project request model for API."""
    project_description: str
    request_type: str = "full"  # "full" or "partial"
    requirements: List[str] = []
    constraints: Dict[str, Any] = {}
    deadline: str = None

class ProjectResponseModel(BaseModel):
    """Project response model for API."""
    success: bool
    project_id: str = None
    request_type: str
    workflow_executed: List[str]
    results: Dict[str, Any]
    error: str = None

class AgentStatusModel(BaseModel):
    """Agent status model for API."""
    agent_name: str
    status: str
    details: Dict[str, Any] = {}
    error: str = None

# Create FastAPI app
app = FastAPI(
    title="Multi-Agent System Client",
    description="Client router for the multi-agent task generation and scheduling system",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add HTTP logging middleware
app.add_middleware(HTTPLoggingMiddleware, agent_name="client_agent")

# Global ACPCallingAgent instance
calling_agent: ACPCallingAgent = None

@app.on_event("startup")
async def startup_event():
    """Initialize the calling agent on startup."""
    global calling_agent
    
    # Setup logging
    setup_agent_logging("client_agent")
    logger.info("Starting Client Router Agent...")
    
    # Initialize ACPCallingAgent
    calling_agent = ACPCallingAgent()
    
    logger.info("Client Router Agent started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global calling_agent
    
    if calling_agent:
        await calling_agent.close()
    
    logger.info("Client Router Agent shutdown completed")

@app.get("/")
async def root():
    """Root endpoint with system information."""
    return {
        "message": "Multi-Agent System Client Router",
        "version": "1.0.0",
        "available_endpoints": [
            "/process-project",
            "/agents/status",
            "/agents/list",
            "/request-types",
            "/health"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "agent_id": "client_agent",
        "timestamp": str(asyncio.get_event_loop().time())
    }

@app.post("/process-project", response_model=ProjectResponseModel)
async def process_project(request: ProjectRequestModel):
    """Process a project request through the multi-agent system.
    
    This endpoint routes requests to appropriate agents based on request type:
    - "full": Task generation + scheduling
    - "partial": Task generation only
    """
    logger.info(f"Received project request: {request.request_type}")
    logger.info(f"Project description: {request.project_description[:100]}...")
    logger.debug(f"Full request: {request.dict()}")
    
    try:
        # Validate request type
        if request.request_type not in REQUEST_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid request type: {request.request_type}. Must be one of: {list(REQUEST_TYPES.keys())}"
            )
        
        # Process request through ACPCallingAgent
        result = await calling_agent.process_project_request(request.dict())
        
        logger.info("Project request processed successfully")
        logger.debug(f"Result: {result}")
        
        return ProjectResponseModel(
            success=True,
            project_id=result.get("results", {}).get("task_generation", {}).get("project_id"),
            request_type=result["request_type"],
            workflow_executed=result["workflow_executed"],
            results=result["results"]
        )
        
    except Exception as e:
        logger.error(f"Project request failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/agents/status/{agent_name}", response_model=AgentStatusModel)
async def get_agent_status(agent_name: str):
    """Get status of a specific agent."""
    logger.info(f"Checking status of agent: {agent_name}")
    
    try:
        status = await calling_agent.get_agent_status(agent_name)
        
        logger.info(f"Agent {agent_name} status: {status['status']}")
        
        return AgentStatusModel(
            agent_name=status["agent_name"],
            status=status["status"],
            details=status.get("details", {}),
            error=status.get("error")
        )
        
    except Exception as e:
        logger.error(f"Failed to get agent status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/agents/list")
async def list_agents():
    """List all available agents and their capabilities."""
    logger.info("Listing all available agents")
    
    try:
        agents = await calling_agent.list_available_agents()
        
        logger.info(f"Found {len(agents)} agents")
        
        return {
            "agents": agents,
            "total_count": len(agents)
        }
        
    except Exception as e:
        logger.error(f"Failed to list agents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/request-types")
async def get_request_types():
    """Get available request types and their descriptions."""
    return {
        "request_types": REQUEST_TYPES,
        "default": "full"
    }

@app.post("/examples/full-request")
async def example_full_request():
    """Example endpoint demonstrating a full request (task generation + scheduling)."""
    logger.info("Processing example full request")
    
    example_request = ProjectRequestModel(
        project_description="Build a modern web application with user authentication, dashboard, and reporting features",
        request_type="full",
        requirements=[
            "User authentication and authorization",
            "Responsive web design",
            "Database integration",
            "Security best practices",
            "Automated testing"
        ],
        constraints={
            "budget": "medium",
            "timeline": "normal"
        },
        deadline="2024-12-31"
    )
    
    return await process_project(example_request)

@app.post("/examples/partial-request")
async def example_partial_request():
    """Example endpoint demonstrating a partial request (task generation only)."""
    logger.info("Processing example partial request")
    
    example_request = ProjectRequestModel(
        project_description="Create a mobile app for task management with offline capabilities",
        request_type="partial",
        requirements=[
            "Offline functionality",
            "Cross-platform compatibility",
            "Data synchronization",
            "Push notifications"
        ],
        constraints={
            "budget": "low",
            "platform": "mobile"
        }
    )
    
    return await process_project(example_request)

@app.get("/logs/recent")
async def get_recent_logs():
    """Get recent log entries (for debugging)."""
    # This is a simplified implementation
    # In production, you'd want proper log management
    return {
        "message": "Log endpoint - check log files for detailed information",
        "log_files": [
            "logs/client_agent.log",
            "logs/task_generation_agent.log", 
            "logs/scheduling_agent.log"
        ]
    }

def main():
    """Main function to run the Client Router Agent."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Client Router Agent")
    parser.add_argument("--port", type=int, default=settings.CLIENT_AGENT_PORT, help="Port to run the agent on")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    
    args = parser.parse_args()
    
    logger.info(f"Starting Client Router Agent on {args.host}:{args.port}")
    
    uvicorn.run(
        "client_agent:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )

if __name__ == "__main__":
    main()
