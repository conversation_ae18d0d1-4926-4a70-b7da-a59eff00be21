"""Example requests and usage patterns for the multi-agent system."""

import asyncio
import json
import httpx
from typing import Dict, Any, List
from datetime import datetime

# Example request configurations
EXAMPLE_REQUESTS = {
    "web_app_full": {
        "project_description": "Build a modern web application with user authentication, dashboard, and reporting features",
        "request_type": "full",
        "requirements": [
            "User authentication and authorization",
            "Responsive web design",
            "Database integration",
            "Security best practices",
            "Automated testing",
            "API documentation"
        ],
        "constraints": {
            "budget": "medium",
            "timeline": "normal",
            "team_size": 3
        },
        "deadline": "2024-12-31"
    },
    
    "mobile_app_partial": {
        "project_description": "Create a mobile app for task management with offline capabilities",
        "request_type": "partial",
        "requirements": [
            "Offline functionality",
            "Cross-platform compatibility",
            "Data synchronization",
            "Push notifications",
            "User-friendly interface"
        ],
        "constraints": {
            "budget": "low",
            "platform": "mobile",
            "target_audience": "professionals"
        }
    },
    
    "ecommerce_full": {
        "project_description": "Develop a complete e-commerce platform with payment processing and inventory management",
        "request_type": "full",
        "requirements": [
            "Payment gateway integration",
            "Inventory management system",
            "Order tracking",
            "Customer support chat",
            "Analytics dashboard",
            "Multi-vendor support"
        ],
        "constraints": {
            "budget": "high",
            "timeline": "tight",
            "compliance": ["PCI DSS", "GDPR"],
            "scalability": "high"
        },
        "deadline": "2024-10-15"
    },
    
    "api_service_partial": {
        "project_description": "Build a RESTful API service for data processing and analytics",
        "request_type": "partial",
        "requirements": [
            "RESTful API design",
            "Data validation",
            "Rate limiting",
            "Authentication",
            "Comprehensive documentation"
        ],
        "constraints": {
            "budget": "medium",
            "performance": "high",
            "technology": "microservices"
        }
    },
    
    "ai_chatbot_full": {
        "project_description": "Create an AI-powered chatbot for customer service with natural language processing",
        "request_type": "full",
        "requirements": [
            "Natural language processing",
            "Intent recognition",
            "Multi-language support",
            "Integration with existing systems",
            "Analytics and reporting",
            "Continuous learning"
        ],
        "constraints": {
            "budget": "high",
            "timeline": "normal",
            "ai_model": "custom",
            "accuracy_requirement": "95%"
        },
        "deadline": "2025-03-01"
    }
}

class MultiAgentClient:
    """Client for interacting with the multi-agent system."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the client.
        
        Args:
            base_url: Base URL of the client agent
        """
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=120.0)
    
    async def process_project(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send a project request to the multi-agent system.
        
        Args:
            request_data: Project request data
            
        Returns:
            Response from the system
        """
        print(f"🚀 Sending {request_data['request_type']} request to multi-agent system...")
        print(f"📝 Project: {request_data['project_description'][:80]}...")
        
        try:
            response = await self.client.post(
                f"{self.base_url}/process-project",
                json=request_data
            )
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ Request processed successfully!")
            return result
            
        except httpx.HTTPStatusError as e:
            print(f"❌ HTTP Error: {e.response.status_code}")
            print(f"Response: {e.response.text}")
            raise
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            raise
    
    async def get_agent_status(self, agent_name: str) -> Dict[str, Any]:
        """Get status of a specific agent.
        
        Args:
            agent_name: Name of the agent
            
        Returns:
            Agent status information
        """
        try:
            response = await self.client.get(f"{self.base_url}/agents/status/{agent_name}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Failed to get status for {agent_name}: {str(e)}")
            return {"error": str(e)}
    
    async def list_agents(self) -> Dict[str, Any]:
        """List all available agents.
        
        Returns:
            List of agents and their information
        """
        try:
            response = await self.client.get(f"{self.base_url}/agents/list")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Failed to list agents: {str(e)}")
            return {"error": str(e)}
    
    async def close(self):
        """Close the client."""
        await self.client.aclose()

def print_request_details(request_name: str, request_data: Dict[str, Any]):
    """Print detailed information about a request."""
    print(f"\n{'='*60}")
    print(f"📋 REQUEST: {request_name.upper()}")
    print(f"{'='*60}")
    print(f"Type: {request_data['request_type'].upper()}")
    print(f"Description: {request_data['project_description']}")
    print(f"\nRequirements:")
    for req in request_data.get('requirements', []):
        print(f"  • {req}")
    print(f"\nConstraints:")
    for key, value in request_data.get('constraints', {}).items():
        print(f"  • {key}: {value}")
    if request_data.get('deadline'):
        print(f"\nDeadline: {request_data['deadline']}")

def print_response_summary(response: Dict[str, Any]):
    """Print a summary of the response."""
    print(f"\n{'='*60}")
    print(f"📊 RESPONSE SUMMARY")
    print(f"{'='*60}")
    print(f"Success: {'✅' if response.get('success') else '❌'}")
    print(f"Request Type: {response.get('request_type', 'unknown').upper()}")
    print(f"Workflow: {' → '.join(response.get('workflow_executed', []))}")
    
    if response.get('project_id'):
        print(f"Project ID: {response['project_id']}")
    
    results = response.get('results', {})
    
    # Task Generation Results
    if 'task_generation' in results:
        task_gen = results['task_generation']
        tasks = task_gen.get('tasks', [])
        print(f"\n📝 TASK GENERATION:")
        print(f"  • Generated {len(tasks)} tasks")
        print(f"  • Total estimated duration: {task_gen.get('total_estimated_duration', 'unknown')}")
        print(f"  • Summary: {task_gen.get('summary', 'No summary')}")
        
        if tasks:
            print(f"\n  Top 3 Tasks:")
            for i, task in enumerate(tasks[:3], 1):
                print(f"    {i}. {task.get('title', 'Untitled')} ({task.get('priority', 'medium')} priority)")
    
    # Scheduling Results
    if 'scheduling' in results:
        scheduling = results['scheduling']
        schedule = scheduling.get('schedule', [])
        print(f"\n📅 SCHEDULING:")
        print(f"  • Created {len(schedule)} schedule items")
        print(f"  • Total duration: {scheduling.get('total_duration', 'unknown')}")
        print(f"  • Timeline: {scheduling.get('timeline_summary', 'No timeline')}")
        
        critical_path = scheduling.get('critical_path', [])
        if critical_path:
            print(f"  • Critical path: {len(critical_path)} tasks")

async def run_example(client: MultiAgentClient, example_name: str):
    """Run a specific example request.
    
    Args:
        client: MultiAgentClient instance
        example_name: Name of the example to run
    """
    if example_name not in EXAMPLE_REQUESTS:
        print(f"❌ Unknown example: {example_name}")
        print(f"Available examples: {list(EXAMPLE_REQUESTS.keys())}")
        return
    
    request_data = EXAMPLE_REQUESTS[example_name]
    
    # Print request details
    print_request_details(example_name, request_data)
    
    # Send request
    try:
        response = await client.process_project(request_data)
        print_response_summary(response)
        
        # Save response to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logs/example_{example_name}_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(response, f, indent=2, default=str)
        print(f"\n💾 Response saved to: {filename}")
        
    except Exception as e:
        print(f"❌ Example failed: {str(e)}")

async def run_all_examples(client: MultiAgentClient):
    """Run all example requests.
    
    Args:
        client: MultiAgentClient instance
    """
    print("🚀 Running all example requests...")
    
    for example_name in EXAMPLE_REQUESTS.keys():
        await run_example(client, example_name)
        print("\n" + "="*80 + "\n")
        
        # Wait between requests to avoid overwhelming the system
        await asyncio.sleep(2)

async def check_system_status(client: MultiAgentClient):
    """Check the status of all agents in the system.
    
    Args:
        client: MultiAgentClient instance
    """
    print("🔍 Checking system status...")
    
    # List all agents
    agents_info = await client.list_agents()
    if 'error' in agents_info:
        print(f"❌ Failed to list agents: {agents_info['error']}")
        return
    
    agents = agents_info.get('agents', [])
    print(f"\n📊 Found {len(agents)} agents:")
    
    for agent in agents:
        status_icon = "✅" if agent.get('status') == 'active' else "❌"
        print(f"  {status_icon} {agent.get('name', 'Unknown')}")
        print(f"     URL: {agent.get('url', 'Unknown')}")
        print(f"     Framework: {agent.get('framework', 'Unknown')}")
        print(f"     Capabilities: {', '.join(agent.get('capabilities', []))}")
        print()

async def main():
    """Main function to demonstrate the multi-agent system."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Multi-Agent System Examples")
    parser.add_argument("--base-url", default="http://localhost:8000", 
                       help="Base URL of the client agent")
    parser.add_argument("--example", choices=list(EXAMPLE_REQUESTS.keys()) + ["all"], 
                       help="Specific example to run")
    parser.add_argument("--status", action="store_true", 
                       help="Check system status")
    
    args = parser.parse_args()
    
    client = MultiAgentClient(args.base_url)
    
    try:
        if args.status:
            await check_system_status(client)
        elif args.example:
            if args.example == "all":
                await run_all_examples(client)
            else:
                await run_example(client, args.example)
        else:
            # Interactive mode
            print("🤖 Multi-Agent System Examples")
            print("\nAvailable examples:")
            for i, (name, data) in enumerate(EXAMPLE_REQUESTS.items(), 1):
                print(f"  {i}. {name} ({data['request_type']})")
            
            print(f"\n  {len(EXAMPLE_REQUESTS) + 1}. Check system status")
            print(f"  {len(EXAMPLE_REQUESTS) + 2}. Run all examples")
            
            choice = input(f"\nSelect an option (1-{len(EXAMPLE_REQUESTS) + 2}): ")
            
            try:
                choice_num = int(choice)
                if 1 <= choice_num <= len(EXAMPLE_REQUESTS):
                    example_name = list(EXAMPLE_REQUESTS.keys())[choice_num - 1]
                    await run_example(client, example_name)
                elif choice_num == len(EXAMPLE_REQUESTS) + 1:
                    await check_system_status(client)
                elif choice_num == len(EXAMPLE_REQUESTS) + 2:
                    await run_all_examples(client)
                else:
                    print("❌ Invalid choice")
            except ValueError:
                print("❌ Invalid input")
    
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(main())
