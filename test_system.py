#!/usr/bin/env python3
"""Comprehensive test suite for the multi-agent system."""

import asyncio
import json
import time
from typing import Dict, Any, List
import httpx
import pytest
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
TASK_GENERATION_URL = "http://localhost:8001"
SCHEDULING_URL = "http://localhost:8002"

class SystemTester:
    """Comprehensive system tester for the multi-agent system."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
        self.test_results = []
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all system tests.
        
        Returns:
            Test results summary
        """
        print("🧪 Starting comprehensive system tests...")
        start_time = time.time()
        
        tests = [
            ("Health Checks", self.test_health_checks),
            ("Agent Communication", self.test_agent_communication),
            ("Full Request Workflow", self.test_full_request),
            ("Partial Request Workflow", self.test_partial_request),
            ("Error Handling", self.test_error_handling),
            ("Request Validation", self.test_request_validation),
            ("Concurrent Requests", self.test_concurrent_requests),
            ("System Status", self.test_system_status)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            print(f"\n{'='*60}")
            print(f"🔍 Running: {test_name}")
            print(f"{'='*60}")
            
            try:
                result = await test_func()
                if result.get("success", False):
                    print(f"✅ {test_name}: PASSED")
                    passed += 1
                else:
                    print(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    failed += 1
                
                self.test_results.append({
                    "test_name": test_name,
                    "success": result.get("success", False),
                    "details": result,
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {str(e)}")
                failed += 1
                self.test_results.append({
                    "test_name": test_name,
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        end_time = time.time()
        duration = end_time - start_time
        
        summary = {
            "total_tests": len(tests),
            "passed": passed,
            "failed": failed,
            "success_rate": (passed / len(tests)) * 100,
            "duration_seconds": duration,
            "results": self.test_results
        }
        
        print(f"\n{'='*60}")
        print(f"📊 TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']}")
        print(f"Failed: {summary['failed']}")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Duration: {summary['duration_seconds']:.2f} seconds")
        
        return summary
    
    async def test_health_checks(self) -> Dict[str, Any]:
        """Test health check endpoints for all agents."""
        agents = [
            ("Client Agent", f"{self.base_url}/health"),
            ("Task Generation Agent", f"{TASK_GENERATION_URL}/health"),
            ("Scheduling Agent", f"{SCHEDULING_URL}/health")
        ]
        
        results = {}
        all_healthy = True
        
        for agent_name, health_url in agents:
            try:
                response = await self.client.get(health_url)
                if response.status_code == 200:
                    data = response.json()
                    results[agent_name] = {
                        "status": "healthy",
                        "response": data
                    }
                    print(f"  ✅ {agent_name}: Healthy")
                else:
                    results[agent_name] = {
                        "status": "unhealthy",
                        "status_code": response.status_code
                    }
                    print(f"  ❌ {agent_name}: Unhealthy (HTTP {response.status_code})")
                    all_healthy = False
            except Exception as e:
                results[agent_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"  ❌ {agent_name}: Error - {str(e)}")
                all_healthy = False
        
        return {
            "success": all_healthy,
            "results": results
        }
    
    async def test_agent_communication(self) -> Dict[str, Any]:
        """Test direct ACP communication with agents."""
        # Test Task Generation Agent
        task_gen_payload = {
            "message_id": "test-001",
            "message_type": "request",
            "sender_id": "test_client",
            "receiver_id": "task_generation_agent",
            "action": "generate_tasks",
            "payload": {
                "project_description": "Test project for ACP communication",
                "requirements": ["testing"],
                "constraints": {}
            }
        }
        
        try:
            response = await self.client.post(
                f"{TASK_GENERATION_URL}/acp/message",
                json=task_gen_payload
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print("  ✅ Task Generation Agent ACP communication successful")
                    return {"success": True, "task_generation": data}
                else:
                    print(f"  ❌ Task Generation Agent returned error: {data.get('error')}")
                    return {"success": False, "error": data.get('error')}
            else:
                print(f"  ❌ Task Generation Agent HTTP error: {response.status_code}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"  ❌ Task Generation Agent communication failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def test_full_request(self) -> Dict[str, Any]:
        """Test full request workflow (task generation + scheduling)."""
        request_data = {
            "project_description": "Test project for full workflow validation",
            "request_type": "full",
            "requirements": ["testing", "validation"],
            "constraints": {"budget": "medium", "timeline": "normal"}
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/process-project",
                json=request_data
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Validate response structure
                required_fields = ["success", "request_type", "workflow_executed", "results"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    return {
                        "success": False,
                        "error": f"Missing fields: {missing_fields}"
                    }
                
                # Validate workflow execution
                expected_workflow = ["task_generation", "scheduling"]
                actual_workflow = data.get("workflow_executed", [])
                
                if actual_workflow != expected_workflow:
                    return {
                        "success": False,
                        "error": f"Unexpected workflow: {actual_workflow}"
                    }
                
                # Validate results structure
                results = data.get("results", {})
                if "task_generation" not in results or "scheduling" not in results:
                    return {
                        "success": False,
                        "error": "Missing task_generation or scheduling results"
                    }
                
                print("  ✅ Full request workflow completed successfully")
                return {"success": True, "response": data}
                
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_partial_request(self) -> Dict[str, Any]:
        """Test partial request workflow (task generation only)."""
        request_data = {
            "project_description": "Test project for partial workflow validation",
            "request_type": "partial",
            "requirements": ["testing"],
            "constraints": {"budget": "low"}
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/process-project",
                json=request_data
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Validate workflow execution
                expected_workflow = ["task_generation"]
                actual_workflow = data.get("workflow_executed", [])
                
                if actual_workflow != expected_workflow:
                    return {
                        "success": False,
                        "error": f"Unexpected workflow: {actual_workflow}"
                    }
                
                # Validate results structure
                results = data.get("results", {})
                if "task_generation" not in results:
                    return {
                        "success": False,
                        "error": "Missing task_generation results"
                    }
                
                if "scheduling" in results:
                    return {
                        "success": False,
                        "error": "Unexpected scheduling results in partial request"
                    }
                
                print("  ✅ Partial request workflow completed successfully")
                return {"success": True, "response": data}
                
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """Test error handling for invalid requests."""
        # Test invalid request type
        invalid_request = {
            "project_description": "Test project",
            "request_type": "invalid_type"
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/process-project",
                json=invalid_request
            )
            
            if response.status_code == 400:
                print("  ✅ Invalid request type properly rejected")
                return {"success": True, "error_handling": "working"}
            else:
                return {
                    "success": False,
                    "error": f"Expected 400, got {response.status_code}"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_request_validation(self) -> Dict[str, Any]:
        """Test request validation."""
        # Test missing required fields
        incomplete_request = {
            "request_type": "full"
            # Missing project_description
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/process-project",
                json=incomplete_request
            )
            
            if response.status_code == 422:  # Validation error
                print("  ✅ Request validation working correctly")
                return {"success": True, "validation": "working"}
            else:
                return {
                    "success": False,
                    "error": f"Expected 422, got {response.status_code}"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_concurrent_requests(self) -> Dict[str, Any]:
        """Test handling of concurrent requests."""
        request_data = {
            "project_description": "Concurrent test project",
            "request_type": "partial",
            "requirements": ["concurrency_test"]
        }
        
        # Send 3 concurrent requests
        tasks = []
        for i in range(3):
            task = self.client.post(
                f"{self.base_url}/process-project",
                json=request_data
            )
            tasks.append(task)
        
        try:
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            successful_responses = 0
            for response in responses:
                if isinstance(response, httpx.Response) and response.status_code == 200:
                    successful_responses += 1
            
            if successful_responses == 3:
                print(f"  ✅ All {successful_responses} concurrent requests succeeded")
                return {"success": True, "concurrent_requests": successful_responses}
            else:
                return {
                    "success": False,
                    "error": f"Only {successful_responses}/3 requests succeeded"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_system_status(self) -> Dict[str, Any]:
        """Test system status endpoints."""
        try:
            # Test agent list endpoint
            response = await self.client.get(f"{self.base_url}/agents/list")
            
            if response.status_code == 200:
                data = response.json()
                agents = data.get("agents", [])
                
                if len(agents) >= 2:  # Should have at least task_generation and scheduling
                    print(f"  ✅ System status: {len(agents)} agents available")
                    return {"success": True, "agents": agents}
                else:
                    return {
                        "success": False,
                        "error": f"Expected at least 2 agents, found {len(agents)}"
                    }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def close(self):
        """Close the test client."""
        await self.client.aclose()

async def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Multi-Agent System Test Suite")
    parser.add_argument("--base-url", default=BASE_URL, help="Base URL of the client agent")
    parser.add_argument("--save-results", action="store_true", help="Save test results to file")
    
    args = parser.parse_args()
    
    tester = SystemTester(args.base_url)
    
    try:
        results = await tester.run_all_tests()
        
        if args.save_results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/test_results_{timestamp}.json"
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"\n💾 Test results saved to: {filename}")
        
        # Exit with appropriate code
        if results["success_rate"] == 100:
            print("\n🎉 All tests passed!")
            exit(0)
        else:
            print(f"\n⚠️  {results['failed']} tests failed")
            exit(1)
            
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
