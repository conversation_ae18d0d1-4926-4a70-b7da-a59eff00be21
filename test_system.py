"""Test the multi-agent system"""

import asyncio
import httpx
from colorama import Fore

async def test_full_request():
    """Test full request (task generation + scheduling)"""
    
    print(f"{Fore.CYAN}=== Testing FULL Request ==={Fore.RESET}")
    
    request_data = {
        "project_description": "Build a modern web application with user authentication, dashboard, and reporting features"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:8000/process-full",
                json=request_data,
                timeout=120.0
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"{Fore.GREEN}✅ Full request successful!{Fore.RESET}")
                print(f"Result: {result['result']}")
                return True
            else:
                print(f"{Fore.RED}❌ Full request failed: {response.status_code}{Fore.RESET}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Full request error: {str(e)}{Fore.RESET}")
            return False

async def test_partial_request():
    """Test partial request (task generation only)"""
    
    print(f"{Fore.CYAN}=== Testing PARTIAL Request ==={Fore.RESET}")
    
    request_data = {
        "project_description": "Create a mobile app for task management with offline capabilities"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:8000/process-partial",
                json=request_data,
                timeout=120.0
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"{Fore.GREEN}✅ Partial request successful!{Fore.RESET}")
                print(f"Result: {result['result']}")
                return True
            else:
                print(f"{Fore.RED}❌ Partial request failed: {response.status_code}{Fore.RESET}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Partial request error: {str(e)}{Fore.RESET}")
            return False

async def test_health_checks():
    """Test health endpoints"""
    
    print(f"{Fore.CYAN}=== Testing Health Checks ==={Fore.RESET}")
    
    endpoints = [
        ("Client Agent", "http://localhost:8000/health"),
        ("Task Generation Agent", "http://localhost:8001/health"),
        ("Scheduling Agent", "http://localhost:8002/health")
    ]
    
    all_healthy = True
    
    async with httpx.AsyncClient() as client:
        for name, url in endpoints:
            try:
                response = await client.get(url, timeout=10.0)
                if response.status_code == 200:
                    print(f"{Fore.GREEN}✅ {name}: Healthy{Fore.RESET}")
                else:
                    print(f"{Fore.RED}❌ {name}: Unhealthy ({response.status_code}){Fore.RESET}")
                    all_healthy = False
            except Exception as e:
                print(f"{Fore.RED}❌ {name}: Error - {str(e)}{Fore.RESET}")
                all_healthy = False
    
    return all_healthy

async def main():
    """Run all tests"""
    
    print(f"{Fore.YELLOW}🧪 Multi-Agent System Test Suite{Fore.RESET}")
    print("=" * 50)
    
    # Test health checks first
    health_ok = await test_health_checks()
    
    if not health_ok:
        print(f"\n{Fore.RED}⚠️  Some agents are not healthy. Please start all agents first:{Fore.RESET}")
        print("1. python task_generation_agent.py")
        print("2. python scheduling_agent.py") 
        print("3. python client_agent.py")
        return
    
    print(f"\n{Fore.GREEN}🎉 All agents are healthy! Running tests...{Fore.RESET}")
    
    # Test partial request
    partial_ok = await test_partial_request()
    
    # Test full request
    full_ok = await test_full_request()
    
    # Summary
    print(f"\n{Fore.YELLOW}📊 Test Results:{Fore.RESET}")
    print(f"Health Checks: {'✅' if health_ok else '❌'}")
    print(f"Partial Request: {'✅' if partial_ok else '❌'}")
    print(f"Full Request: {'✅' if full_ok else '❌'}")
    
    if health_ok and partial_ok and full_ok:
        print(f"\n{Fore.GREEN}🎉 All tests passed! The multi-agent system is working correctly.{Fore.RESET}")
    else:
        print(f"\n{Fore.RED}❌ Some tests failed. Please check the logs above.{Fore.RESET}")

if __name__ == "__main__":
    asyncio.run(main())
