"""Example usage of the multi-agent system with OpenRouter"""

import asyncio
import httpx
from colorama import Fore

async def test_full_request():
    """Test full request with a web application project"""
    
    print(f"{Fore.CYAN}🚀 Testing FULL Request (Task Generation + Scheduling){Fore.RESET}")
    
    request_data = {
        "project_description": "Build a modern e-commerce web application with user authentication, product catalog, shopping cart, payment processing, and admin dashboard"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            print(f"{Fore.YELLOW}📤 Sending request...{Fore.RESET}")
            response = await client.post(
                "http://localhost:8000/process-full",
                json=request_data,
                timeout=180.0
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"{Fore.GREEN}✅ Success!{Fore.RESET}")
                print(f"\n📋 Project: {result['project_description']}")
                print(f"🔄 Type: {result['request_type']}")
                print(f"\n📝 Result:\n{result['result']}")
                return True
            else:
                print(f"{Fore.RED}❌ Failed: {response.status_code}{Fore.RESET}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Error: {str(e)}{Fore.RESET}")
            return False

async def test_partial_request():
    """Test partial request with a mobile app project"""
    
    print(f"\n{Fore.CYAN}🚀 Testing PARTIAL Request (Task Generation Only){Fore.RESET}")
    
    request_data = {
        "project_description": "Create a mobile fitness tracking app with workout plans, progress tracking, and social features"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            print(f"{Fore.YELLOW}📤 Sending request...{Fore.RESET}")
            response = await client.post(
                "http://localhost:8000/process-partial",
                json=request_data,
                timeout=180.0
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"{Fore.GREEN}✅ Success!{Fore.RESET}")
                print(f"\n📋 Project: {result['project_description']}")
                print(f"🔄 Type: {result['request_type']}")
                print(f"\n📝 Result:\n{result['result']}")
                return True
            else:
                print(f"{Fore.RED}❌ Failed: {response.status_code}{Fore.RESET}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Error: {str(e)}{Fore.RESET}")
            return False

async def check_system_health():
    """Check if all agents are running"""
    
    print(f"{Fore.CYAN}🏥 Checking System Health{Fore.RESET}")
    
    endpoints = [
        ("Client Agent", "http://localhost:8000/health"),
        ("Task Generation Agent", "http://localhost:8001/health"),
        ("Scheduling Agent", "http://localhost:8002/health")
    ]
    
    all_healthy = True
    
    async with httpx.AsyncClient() as client:
        for name, url in endpoints:
            try:
                response = await client.get(url, timeout=10.0)
                if response.status_code == 200:
                    print(f"{Fore.GREEN}✅ {name}: Healthy{Fore.RESET}")
                else:
                    print(f"{Fore.RED}❌ {name}: Unhealthy ({response.status_code}){Fore.RESET}")
                    all_healthy = False
            except Exception as e:
                print(f"{Fore.RED}❌ {name}: Error - {str(e)}{Fore.RESET}")
                all_healthy = False
    
    return all_healthy

async def main():
    """Run example usage"""
    
    print(f"{Fore.YELLOW}🤖 Multi-Agent System with OpenRouter - Example Usage{Fore.RESET}")
    print("=" * 60)
    
    # Check system health first
    health_ok = await check_system_health()
    
    if not health_ok:
        print(f"\n{Fore.RED}⚠️  Some agents are not running. Please start them first:{Fore.RESET}")
        print("1. python task_generation_agent.py")
        print("2. python scheduling_agent.py")
        print("3. python client_agent.py")
        print(f"\n{Fore.YELLOW}💡 Or use: python start_agents.py{Fore.RESET}")
        return
    
    print(f"\n{Fore.GREEN}🎉 All agents are healthy! Running examples...{Fore.RESET}")
    
    # Test partial request (task generation only)
    partial_ok = await test_partial_request()
    
    # Test full request (task generation + scheduling)
    full_ok = await test_full_request()
    
    # Summary
    print(f"\n{Fore.YELLOW}📊 Results Summary:{Fore.RESET}")
    print(f"System Health: {'✅' if health_ok else '❌'}")
    print(f"Partial Request: {'✅' if partial_ok else '❌'}")
    print(f"Full Request: {'✅' if full_ok else '❌'}")
    
    if health_ok and partial_ok and full_ok:
        print(f"\n{Fore.GREEN}🎉 All examples completed successfully!{Fore.RESET}")
        print(f"{Fore.GREEN}The multi-agent system with OpenRouter is working perfectly.{Fore.RESET}")
    else:
        print(f"\n{Fore.RED}❌ Some examples failed. Check the logs above.{Fore.RESET}")

if __name__ == "__main__":
    print(f"{Fore.BLUE}💡 Make sure to set your OPENROUTER_API_KEY in .env file{Fore.RESET}")
    asyncio.run(main())
