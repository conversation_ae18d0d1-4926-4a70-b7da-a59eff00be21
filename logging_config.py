"""Comprehensive logging configuration for the multi-agent system."""

import sys
import json
from typing import Dict, Any
from datetime import datetime
from loguru import logger
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware

class LoggingConfig:
    """Centralized logging configuration."""
    
    @staticmethod
    def setup_logger(agent_name: str, log_level: str = "INFO"):
        """Setup logger for an agent.
        
        Args:
            agent_name: Name of the agent
            log_level: Logging level
        """
        # Remove default logger
        logger.remove()
        
        # Console logging with colors
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> | <level>{message}</level>",
            level=log_level,
            colorize=True
        )
        
        # File logging for this agent
        logger.add(
            f"logs/{agent_name}.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name} | {message}",
            level=log_level,
            rotation="1 day",
            retention="7 days",
            compression="zip"
        )
        
        # JSON logging for structured logs
        logger.add(
            f"logs/{agent_name}_structured.json",
            format="{message}",
            level=log_level,
            rotation="1 day",
            retention="7 days",
            serialize=True
        )
        
        logger.info(f"Logging configured for {agent_name}")

class HTTPLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log all HTTP requests and responses."""
    
    def __init__(self, app, agent_name: str):
        super().__init__(app)
        self.agent_name = agent_name
    
    async def dispatch(self, request: Request, call_next):
        """Log HTTP request and response."""
        start_time = datetime.utcnow()
        
        # Log request
        await self._log_request(request, start_time)
        
        # Process request
        response = await call_next(request)
        
        # Log response
        end_time = datetime.utcnow()
        await self._log_response(request, response, start_time, end_time)
        
        return response
    
    async def _log_request(self, request: Request, start_time: datetime):
        """Log incoming HTTP request."""
        # Read request body if present
        body = None
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    body = body.decode('utf-8')
                    # Try to parse as JSON for better logging
                    try:
                        body = json.loads(body)
                    except json.JSONDecodeError:
                        pass
            except Exception:
                body = "<unable to read body>"
        
        # Create request log entry
        log_data = {
            "event": "http_request",
            "agent": self.agent_name,
            "timestamp": start_time.isoformat(),
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers),
            "client_ip": request.client.host if request.client else None,
            "body": body
        }
        
        logger.info(f"HTTP Request: {request.method} {request.url.path}")
        logger.debug(f"Request details: {json.dumps(log_data, indent=2)}")
        
        # Store in request state for response logging
        request.state.log_data = log_data
    
    async def _log_response(self, request: Request, response: Response, start_time: datetime, end_time: datetime):
        """Log HTTP response."""
        duration = (end_time - start_time).total_seconds()
        
        # Get request log data
        request_log_data = getattr(request.state, 'log_data', {})
        
        # Create response log entry
        log_data = {
            "event": "http_response",
            "agent": self.agent_name,
            "timestamp": end_time.isoformat(),
            "request_id": request_log_data.get("request_id"),
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "status_code": response.status_code,
            "headers": dict(response.headers),
            "duration_seconds": duration,
            "success": 200 <= response.status_code < 400
        }
        
        # Log based on status code
        if response.status_code >= 500:
            logger.error(f"HTTP Response: {request.method} {request.url.path} - {response.status_code} ({duration:.3f}s)")
        elif response.status_code >= 400:
            logger.warning(f"HTTP Response: {request.method} {request.url.path} - {response.status_code} ({duration:.3f}s)")
        else:
            logger.info(f"HTTP Response: {request.method} {request.url.path} - {response.status_code} ({duration:.3f}s)")
        
        logger.debug(f"Response details: {json.dumps(log_data, indent=2)}")

class ACPLoggingMixin:
    """Mixin to add ACP communication logging."""
    
    def log_acp_request(self, target_agent: str, action: str, payload: Dict[str, Any], message_id: str):
        """Log outgoing ACP request."""
        log_data = {
            "event": "acp_request_sent",
            "agent": getattr(self, 'agent_id', 'unknown'),
            "timestamp": datetime.utcnow().isoformat(),
            "message_id": message_id,
            "target_agent": target_agent,
            "action": action,
            "payload_size": len(str(payload)),
            "payload": payload
        }
        
        logger.info(f"ACP Request: {action} -> {target_agent} ({message_id})")
        logger.debug(f"ACP Request details: {json.dumps(log_data, indent=2)}")
    
    def log_acp_response(self, request_id: str, success: bool, result: Dict[str, Any] = None, error: str = None):
        """Log ACP response."""
        log_data = {
            "event": "acp_response_received",
            "agent": getattr(self, 'agent_id', 'unknown'),
            "timestamp": datetime.utcnow().isoformat(),
            "request_id": request_id,
            "success": success,
            "result_size": len(str(result)) if result else 0,
            "result": result,
            "error": error
        }
        
        if success:
            logger.info(f"ACP Response: Success for request {request_id}")
        else:
            logger.error(f"ACP Response: Failed for request {request_id} - {error}")
        
        logger.debug(f"ACP Response details: {json.dumps(log_data, indent=2)}")
    
    def log_acp_message_received(self, sender: str, action: str, payload: Dict[str, Any], message_id: str):
        """Log incoming ACP message."""
        log_data = {
            "event": "acp_message_received",
            "agent": getattr(self, 'agent_id', 'unknown'),
            "timestamp": datetime.utcnow().isoformat(),
            "message_id": message_id,
            "sender": sender,
            "action": action,
            "payload_size": len(str(payload)),
            "payload": payload
        }
        
        logger.info(f"ACP Message: {action} from {sender} ({message_id})")
        logger.debug(f"ACP Message details: {json.dumps(log_data, indent=2)}")
    
    def log_acp_message_processed(self, message_id: str, action: str, success: bool, result: Dict[str, Any] = None, error: str = None):
        """Log ACP message processing result."""
        log_data = {
            "event": "acp_message_processed",
            "agent": getattr(self, 'agent_id', 'unknown'),
            "timestamp": datetime.utcnow().isoformat(),
            "message_id": message_id,
            "action": action,
            "success": success,
            "result_size": len(str(result)) if result else 0,
            "result": result,
            "error": error
        }
        
        if success:
            logger.info(f"ACP Processing: Completed {action} ({message_id})")
        else:
            logger.error(f"ACP Processing: Failed {action} ({message_id}) - {error}")
        
        logger.debug(f"ACP Processing details: {json.dumps(log_data, indent=2)}")

class WorkflowLogger:
    """Logger for workflow execution tracking."""
    
    @staticmethod
    def log_workflow_start(workflow_id: str, workflow_type: str, description: str):
        """Log workflow start."""
        log_data = {
            "event": "workflow_start",
            "workflow_id": workflow_id,
            "workflow_type": workflow_type,
            "description": description,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Workflow Started: {workflow_type} ({workflow_id})")
        logger.debug(f"Workflow details: {json.dumps(log_data, indent=2)}")
    
    @staticmethod
    def log_workflow_step(workflow_id: str, step_name: str, step_data: Dict[str, Any]):
        """Log workflow step execution."""
        log_data = {
            "event": "workflow_step",
            "workflow_id": workflow_id,
            "step_name": step_name,
            "step_data": step_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Workflow Step: {step_name} ({workflow_id})")
        logger.debug(f"Step details: {json.dumps(log_data, indent=2)}")
    
    @staticmethod
    def log_workflow_complete(workflow_id: str, success: bool, result: Dict[str, Any] = None, error: str = None):
        """Log workflow completion."""
        log_data = {
            "event": "workflow_complete",
            "workflow_id": workflow_id,
            "success": success,
            "result": result,
            "error": error,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if success:
            logger.info(f"Workflow Completed: {workflow_id}")
        else:
            logger.error(f"Workflow Failed: {workflow_id} - {error}")
        
        logger.debug(f"Workflow completion details: {json.dumps(log_data, indent=2)}")

# Utility functions
def create_logs_directory():
    """Create logs directory if it doesn't exist."""
    import os
    os.makedirs("logs", exist_ok=True)

def setup_agent_logging(agent_name: str, log_level: str = "INFO"):
    """Setup logging for an agent."""
    create_logs_directory()
    LoggingConfig.setup_logger(agent_name, log_level)
