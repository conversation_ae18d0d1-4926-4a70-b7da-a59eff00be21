#!/bin/bash

# Multi-Agent System Deployment Script
# This script deploys agents across multiple servers/processes

set -e

# Configuration
DEFAULT_HOST="0.0.0.0"
PYTHON_CMD="python3"
VENV_PATH="venv"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to check if a port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to wait for a service to be ready
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1
    
    log "Waiting for $service_name to be ready on $host:$port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://$host:$port/health" >/dev/null 2>&1; then
            success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    error "$service_name failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to setup Python environment
setup_environment() {
    log "Setting up Python environment..."
    
    # Check if Python 3 is available
    if ! command -v $PYTHON_CMD &> /dev/null; then
        error "Python 3 is not installed or not in PATH"
        exit 1
    fi
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "$VENV_PATH" ]; then
        log "Creating virtual environment..."
        $PYTHON_CMD -m venv $VENV_PATH
    fi
    
    # Activate virtual environment
    source $VENV_PATH/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install requirements
    if [ -f "requirements.txt" ]; then
        log "Installing Python dependencies..."
        pip install -r requirements.txt
    else
        error "requirements.txt not found"
        exit 1
    fi
    
    success "Python environment setup complete"
}

# Function to create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p scripts
    mkdir -p agents
    mkdir -p acp
    
    success "Directories created"
}

# Function to start a single agent
start_agent() {
    local agent_name=$1
    local host=${2:-$DEFAULT_HOST}
    local port=$3
    local script_path=$4
    
    log "Starting $agent_name on $host:$port..."
    
    # Check if port is available
    if ! check_port $port; then
        error "Port $port is already in use"
        return 1
    fi
    
    # Start the agent in background
    nohup $PYTHON_CMD $script_path --host $host --port $port > logs/${agent_name}.out 2>&1 &
    local pid=$!
    
    # Save PID for later management
    echo $pid > logs/${agent_name}.pid
    
    # Wait for service to be ready
    if wait_for_service $host $port $agent_name; then
        success "$agent_name started successfully (PID: $pid)"
        return 0
    else
        error "Failed to start $agent_name"
        return 1
    fi
}

# Function to stop an agent
stop_agent() {
    local agent_name=$1
    local pid_file="logs/${agent_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat $pid_file)
        log "Stopping $agent_name (PID: $pid)..."
        
        if kill -TERM $pid 2>/dev/null; then
            # Wait for graceful shutdown
            sleep 5
            
            # Check if process is still running
            if kill -0 $pid 2>/dev/null; then
                warning "Graceful shutdown failed, force killing $agent_name"
                kill -KILL $pid 2>/dev/null
            fi
            
            rm -f $pid_file
            success "$agent_name stopped"
        else
            warning "$agent_name was not running or already stopped"
            rm -f $pid_file
        fi
    else
        warning "PID file for $agent_name not found"
    fi
}

# Function to deploy all agents locally
deploy_local() {
    local host=${1:-$DEFAULT_HOST}
    
    log "Deploying all agents locally on $host..."
    
    # Setup environment
    setup_environment
    create_directories
    
    # Start agents in order
    start_agent "task_generation_agent" $host 8001 "agents/task_generation_agent.py"
    start_agent "scheduling_agent" $host 8002 "agents/scheduling_agent.py"
    start_agent "client_agent" $host 8000 "client_agent.py"
    
    success "All agents deployed successfully!"
    
    echo ""
    echo "Agent URLs:"
    echo "  Client Agent: http://$host:8000"
    echo "  Task Generation Agent: http://$host:8001"
    echo "  Scheduling Agent: http://$host:8002"
    echo ""
    echo "Test the system:"
    echo "  curl -X POST http://$host:8000/process-project \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"project_description\": \"Build a web app\", \"request_type\": \"full\"}'"
}

# Function to deploy agents on separate servers
deploy_distributed() {
    log "Distributed deployment not implemented in this script"
    log "For distributed deployment:"
    log "1. Copy the codebase to each server"
    log "2. Run individual agents on each server:"
    log "   Server 1: python agents/task_generation_agent.py --host 0.0.0.0 --port 8001"
    log "   Server 2: python agents/scheduling_agent.py --host 0.0.0.0 --port 8002"
    log "   Server 3: python client_agent.py --host 0.0.0.0 --port 8000"
    log "3. Update config.py with the correct server URLs"
}

# Function to stop all agents
stop_all() {
    log "Stopping all agents..."
    
    stop_agent "client_agent"
    stop_agent "scheduling_agent"
    stop_agent "task_generation_agent"
    
    success "All agents stopped"
}

# Function to show agent status
show_status() {
    log "Agent Status:"
    
    for agent in "task_generation_agent" "scheduling_agent" "client_agent"; do
        local pid_file="logs/${agent}.pid"
        if [ -f "$pid_file" ]; then
            local pid=$(cat $pid_file)
            if kill -0 $pid 2>/dev/null; then
                echo "  $agent: Running (PID: $pid)"
            else
                echo "  $agent: Stopped (stale PID file)"
                rm -f $pid_file
            fi
        else
            echo "  $agent: Stopped"
        fi
    done
}

# Function to show logs
show_logs() {
    local agent_name=${1:-"all"}
    
    if [ "$agent_name" = "all" ]; then
        log "Showing logs for all agents..."
        for agent in "task_generation_agent" "scheduling_agent" "client_agent"; do
            if [ -f "logs/${agent}.out" ]; then
                echo ""
                echo "=== $agent ==="
                tail -n 20 "logs/${agent}.out"
            fi
        done
    else
        if [ -f "logs/${agent_name}.out" ]; then
            tail -f "logs/${agent_name}.out"
        else
            error "Log file for $agent_name not found"
        fi
    fi
}

# Main script logic
case "${1:-help}" in
    "setup")
        setup_environment
        create_directories
        ;;
    "start")
        deploy_local ${2:-$DEFAULT_HOST}
        ;;
    "stop")
        stop_all
        ;;
    "restart")
        stop_all
        sleep 2
        deploy_local ${2:-$DEFAULT_HOST}
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs $2
        ;;
    "distributed")
        deploy_distributed
        ;;
    "help"|*)
        echo "Multi-Agent System Deployment Script"
        echo ""
        echo "Usage: $0 <command> [options]"
        echo ""
        echo "Commands:"
        echo "  setup              Setup Python environment and directories"
        echo "  start [host]       Start all agents locally (default host: 0.0.0.0)"
        echo "  stop               Stop all agents"
        echo "  restart [host]     Restart all agents"
        echo "  status             Show agent status"
        echo "  logs [agent]       Show logs (all agents or specific agent)"
        echo "  distributed        Show distributed deployment instructions"
        echo "  help               Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 setup           # Setup environment"
        echo "  $0 start           # Start all agents on 0.0.0.0"
        echo "  $0 start localhost # Start all agents on localhost"
        echo "  $0 logs client_agent # Show client agent logs"
        ;;
esac
