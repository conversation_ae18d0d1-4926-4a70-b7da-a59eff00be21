# Multi-Agent System with ACP

A distributed multi-agent system using Agent Communication Protocol (ACP) for task generation and scheduling. This system demonstrates how multiple specialized agents can work together to process complex project requests through structured communication protocols.

## 🏗️ Architecture

```
┌─────────────────┐    ACP     ┌──────────────────────┐
│   Client Agent  │ ────────► │ Task Generation Agent │
│  (FastAPI Router)│           │     (CrewAI)         │
└─────────────────┘           └──────────────────────┘
         │                              │
         │                              │ ACP
         │                              ▼
         │                    ┌──────────────────────┐
         │                    │  Scheduling Agent    │
         │                    │   (SmolagentS)       │
         │                    └──────────────────────┘
         │                              │
         │ ◄────────────────────────────┘
         ▼
┌─────────────────┐
│  Final Output   │
│   (Combined)    │
└─────────────────┘
```

## 🤖 Agents

### 1. Client Agent (Port 8000)
- **Framework**: FastAPI + ACPCallingAgent
- **Role**: Request router and response aggregator
- **Capabilities**:
  - Routes requests to appropriate specialized agents
  - <PERSON><PERSON> request type validation
  - Aggregates responses from multiple agents
  - Provides REST API endpoints

### 2. Task Generation Agent (Port 8001)
- **Framework**: CrewAI (mock implementation)
- **Role**: Converts project descriptions into structured task lists
- **Capabilities**:
  - Project analysis and requirement extraction
  - Task breakdown and prioritization
  - Dependency identification
  - Duration estimation

### 3. Scheduling Agent (Port 8002)
- **Framework**: SmolagentS (mock implementation)
- **Role**: Creates time-based schedules from task lists
- **Capabilities**:
  - Timeline planning and optimization
  - Resource allocation
  - Critical path analysis
  - Constraint handling

## ✨ Features

- **ACP-based Communication**: Custom Agent Communication Protocol for reliable inter-agent messaging
- **Distributed Deployment**: Agents can run on separate servers/processes
- **Request Type Support**: Full (task generation + scheduling) and partial (task generation only) workflows
- **Comprehensive Logging**: Detailed logging of all HTTP requests, responses, and agent communications
- **Framework Integration**: Designed to work with OpenRouter, CrewAI, and SmolagentS
- **Error Handling**: Robust error handling and timeout management
- **Health Monitoring**: Built-in health checks and status monitoring

## 🚀 Quick Start

### Installation

```bash
# Clone or download the project
cd multi-agent-system

# Install dependencies
pip install -r requirements.txt

# Create necessary directories
mkdir -p logs
```

### Option 1: Using the Deployment Script (Recommended)

```bash
# Setup environment and start all agents
./scripts/deploy_agents.sh setup
./scripts/deploy_agents.sh start

# Check status
./scripts/deploy_agents.sh status

# Stop all agents
./scripts/deploy_agents.sh stop
```

### Option 2: Manual Startup

```bash
# Terminal 1: Task Generation Agent
python agents/task_generation_agent.py --port 8001

# Terminal 2: Scheduling Agent
python agents/scheduling_agent.py --port 8002

# Terminal 3: Client Agent
python client_agent.py --port 8000
```

### Option 3: Using Python Manager

```bash
# Start all agents
python scripts/start_all_agents.py start

# Check status
python scripts/start_all_agents.py status

# Stop all agents
python scripts/start_all_agents.py stop
```

## 📋 Request Types

### Full Request
Executes complete workflow: Task Generation → Scheduling → Response
```json
{
  "project_description": "Build a web application",
  "request_type": "full",
  "requirements": ["authentication", "database"],
  "constraints": {"budget": "medium", "timeline": "normal"}
}
```

### Partial Request
Executes only task generation: Task Generation → Response
```json
{
  "project_description": "Create a mobile app",
  "request_type": "partial",
  "requirements": ["offline capability"],
  "constraints": {"budget": "low"}
}
```

## 🔗 API Endpoints

### Client Agent (http://localhost:8000)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | System information |
| GET | `/health` | Health check |
| POST | `/process-project` | Main project processing endpoint |
| GET | `/agents/status/{agent_name}` | Get specific agent status |
| GET | `/agents/list` | List all available agents |
| GET | `/request-types` | Get available request types |
| POST | `/examples/full-request` | Example full request |
| POST | `/examples/partial-request` | Example partial request |

### Individual Agents

Each agent exposes:
- `GET /health` - Health check
- `GET /info` - Agent information
- `POST /acp/message` - ACP message handling

## 📁 Directory Structure

```
multi-agent-system/
├── agents/                     # Agent implementations
│   ├── __init__.py
│   ├── base_agent.py          # Base agent class
│   ├── task_generation_agent.py
│   └── scheduling_agent.py
├── acp/                       # ACP Protocol implementation
│   ├── __init__.py
│   ├── protocol.py           # Core ACP protocol
│   ├── calling_agent.py      # ACPCallingAgent
│   └── message_types.py      # Message structures
├── scripts/                   # Deployment scripts
│   ├── start_all_agents.py   # Python agent manager
│   └── deploy_agents.sh      # Bash deployment script
├── logs/                     # Log files (created automatically)
├── client_agent.py          # Main client router
├── config.py                # Configuration settings
├── logging_config.py        # Logging configuration
├── examples.py              # Usage examples
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## 🧪 Testing the System

### Using Examples Script

```bash
# Run interactive examples
python examples.py

# Run specific example
python examples.py --example web_app_full

# Run all examples
python examples.py --example all

# Check system status
python examples.py --status
```

### Using cURL

```bash
# Full request example
curl -X POST http://localhost:8000/process-project \
  -H "Content-Type: application/json" \
  -d '{
    "project_description": "Build a modern web application with user authentication",
    "request_type": "full",
    "requirements": ["authentication", "database", "responsive design"],
    "constraints": {"budget": "medium", "timeline": "normal"}
  }'

# Partial request example
curl -X POST http://localhost:8000/process-project \
  -H "Content-Type: application/json" \
  -d '{
    "project_description": "Create a mobile task management app",
    "request_type": "partial",
    "requirements": ["offline capability", "cross-platform"]
  }'

# Check agent status
curl http://localhost:8000/agents/status/task_generation

# List all agents
curl http://localhost:8000/agents/list
```

## 📊 Communication Flow

### Full Request Flow
```
1. Client sends POST /process-project (request_type: "full")
2. Client Agent validates request
3. Client Agent → Task Generation Agent (ACP: generate_tasks)
4. Task Generation Agent processes and returns task list
5. Client Agent → Scheduling Agent (ACP: create_schedule)
6. Scheduling Agent creates schedule from tasks
7. Client Agent aggregates results and returns to client
```

### Partial Request Flow
```
1. Client sends POST /process-project (request_type: "partial")
2. Client Agent validates request
3. Client Agent → Task Generation Agent (ACP: generate_tasks)
4. Task Generation Agent processes and returns task list
5. Client Agent returns task generation results to client
```

### ACP Message Structure
```json
{
  "message_id": "uuid",
  "message_type": "request|response|error",
  "sender_id": "agent_id",
  "receiver_id": "target_agent_id",
  "timestamp": "ISO_datetime",
  "acp_version": "1.0",
  "action": "generate_tasks|create_schedule",
  "payload": { ... }
}
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file:
```bash
# OpenRouter API (if using real AI models)
OPENROUTER_API_KEY=your_api_key_here

# Logging
LOG_LEVEL=INFO

# Agent URLs (for distributed deployment)
TASK_GENERATION_AGENT_URL=http://localhost:8001
SCHEDULING_AGENT_URL=http://localhost:8002
```

### Agent Ports
- Client Agent: 8000
- Task Generation Agent: 8001
- Scheduling Agent: 8002

## 📝 Logging

The system provides comprehensive logging at multiple levels:

### Log Files
- `logs/client_agent.log` - Client agent logs
- `logs/task_generation_agent.log` - Task generation logs
- `logs/scheduling_agent.log` - Scheduling agent logs
- `logs/*_structured.json` - Structured JSON logs

### Log Levels
- **INFO**: General operation information
- **DEBUG**: Detailed request/response data
- **ERROR**: Error conditions and failures

### Viewing Logs
```bash
# View recent logs for all agents
./scripts/deploy_agents.sh logs

# View specific agent logs
./scripts/deploy_agents.sh logs client_agent

# Follow logs in real-time
tail -f logs/client_agent.log
```

## 🚀 Distributed Deployment

### Single Server Deployment
All agents run on the same server (default configuration).

### Multi-Server Deployment
1. Deploy each agent on separate servers
2. Update `config.py` with correct URLs:
   ```python
   TASK_GENERATION_AGENT_URL = "http://server1:8001"
   SCHEDULING_AGENT_URL = "http://server2:8002"
   ```
3. Start agents individually:
   ```bash
   # Server 1
   python agents/task_generation_agent.py --host 0.0.0.0 --port 8001

   # Server 2
   python agents/scheduling_agent.py --host 0.0.0.0 --port 8002

   # Server 3
   python client_agent.py --host 0.0.0.0 --port 8000
   ```

## 🔍 Monitoring and Health Checks

### Health Check Endpoints
```bash
# Check all agents
curl http://localhost:8000/health
curl http://localhost:8001/health
curl http://localhost:8002/health

# Get detailed agent information
curl http://localhost:8000/agents/list
```

### System Status
```bash
# Using deployment script
./scripts/deploy_agents.sh status

# Using Python manager
python scripts/start_all_agents.py status
```

## 🛠️ Development

### Adding New Agents
1. Create new agent class inheriting from `BaseAgent`
2. Implement required abstract methods
3. Register ACP handlers
4. Add configuration to `config.py`
5. Update deployment scripts

### Extending ACP Protocol
1. Add new message types to `acp/message_types.py`
2. Implement handlers in agent classes
3. Update protocol documentation

### Custom Request Types
1. Add new request type to `config.py`
2. Update workflow logic in `ACPCallingAgent`
3. Add examples to `examples.py`

## 🐛 Troubleshooting

### Common Issues

**Port Already in Use**
```bash
# Find process using port
lsof -i :8000
# Kill process
kill -9 <PID>
```

**Agent Not Responding**
```bash
# Check agent logs
tail -f logs/agent_name.log
# Restart specific agent
./scripts/deploy_agents.sh stop
./scripts/deploy_agents.sh start
```

**ACP Communication Failures**
- Check network connectivity between agents
- Verify agent URLs in configuration
- Check firewall settings for distributed deployment

### Debug Mode
```bash
# Start with debug logging
LOG_LEVEL=DEBUG python client_agent.py
```

## 📚 Examples

The `examples.py` script includes several pre-configured examples:

- **web_app_full**: Complete web application with full workflow
- **mobile_app_partial**: Mobile app with partial workflow
- **ecommerce_full**: E-commerce platform with complex requirements
- **api_service_partial**: RESTful API service
- **ai_chatbot_full**: AI chatbot with advanced features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Update documentation
5. Submit a pull request

## 📄 License

This project is provided as an example implementation. Modify and use as needed for your specific requirements.

## 🔗 Related Technologies

- **FastAPI**: Modern web framework for building APIs
- **CrewAI**: Multi-agent framework for AI applications
- **SmolagentS**: Agent framework for complex workflows
- **OpenRouter**: API gateway for AI models
- **Pydantic**: Data validation and settings management
