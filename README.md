# Multi-Agent System with ACP

Basit bir multi-agent sistem örneği. GitHub'daki [ACPWalkthrough](https://github.com/nicknochnack/ACPWalkthrough) örneğinden esinlenilmiştir.

## Sistem Mimarisi

```
Client Agent (Port 8000)
    ↓ ACP
Task Generation Agent (Port 8001) 
    ↓ ACP  
Scheduling Agent (Port 8002)
    ↓
Final Response
```

## Agentlar

1. **Client Agent** (8000): İstekleri uygun agentlara yönlendirir
2. **Task Generation Agent** (8001): Proje açıklamasından görev listesi oluşturur  
3. **Scheduling Agent** (8002): Görev listesinden zaman planlaması yapar

## <PERSON><PERSON>lum

```bash
pip install -r requirements.txt
```

### OpenRouter API Key Ayarla

`.env` dosyasında OpenRouter API key'inizi ayarlayın:
```bash
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=anthropic/claude-3-haiku
```

## Kullanım

### 1. <PERSON>lar<PERSON> Başlat

```bash
# Terminal 1: Task Generation Agent
python task_generation_agent.py

# Terminal 2: Scheduling Agent  
python scheduling_agent.py

# Terminal 3: Client Agent
python client_agent.py
```

### 2. Test Et

```bash
python test_system.py
```

### 3. Örnek Kullanım

```bash
python example_usage.py
```

### 4. İstek Gönder

**Full Request (Görev oluşturma + Planlama):**
```bash
curl -X POST http://localhost:8000/process-full \
  -H "Content-Type: application/json" \
  -d '{"project_description": "Build a web application"}'
```

**Partial Request (Sadece görev oluşturma):**
```bash
curl -X POST http://localhost:8000/process-partial \
  -H "Content-Type: application/json" \
  -d '{"project_description": "Create a mobile app"}'
```

## İstek Türleri

- **Full**: Görev oluşturma + Planlama
- **Partial**: Sadece görev oluşturma

## Özellikler

- **OpenRouter** ile AI model entegrasyonu
- **ACP protokolü** ile agent iletişimi
- **FastAPI** ile REST API endpoints
- Basit görev oluşturma ve planlama
- Comprehensive logging
- Ayrı sunucularda çalışabilir

## Dosyalar

- `fastacp.py`: ACP protokolü ve ACPCallingAgent implementasyonu
- `openrouter_model.py`: OpenRouter model wrapper
- `task_generation_agent.py`: Görev oluşturma agenti
- `scheduling_agent.py`: Planlama agenti
- `client_agent.py`: Ana client router
- `test_system.py`: Test script
- `example_usage.py`: Örnek kullanım
- `start_agents.py`: Tüm agentları başlatma script
- `.env`: OpenRouter API key konfigürasyonu
- `requirements.txt`: Python bağımlılıkları
