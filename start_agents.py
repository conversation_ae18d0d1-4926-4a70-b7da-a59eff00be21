"""Simple script to start all agents"""

import subprocess
import sys
import time
import os

def start_agent(script_name, port):
    """Start an agent in a new process"""
    print(f"Starting {script_name} on port {port}...")
    
    # Start the process
    process = subprocess.Popen([
        sys.executable, script_name
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    return process

def main():
    """Start all agents"""
    
    print("🚀 Starting Multi-Agent System...")
    print("=" * 40)
    
    agents = [
        ("task_generation_agent.py", 8001),
        ("scheduling_agent.py", 8002), 
        ("client_agent.py", 8000)
    ]
    
    processes = []
    
    for script, port in agents:
        if os.path.exists(script):
            process = start_agent(script, port)
            processes.append((script, process))
            time.sleep(2)  # Wait between starts
        else:
            print(f"❌ {script} not found!")
    
    print(f"\n✅ Started {len(processes)} agents")
    print("\nAgent URLs:")
    print("  Client Agent: http://localhost:8000")
    print("  Task Generation: http://localhost:8001") 
    print("  Scheduling: http://localhost:8002")
    
    print(f"\n🧪 Test the system:")
    print("  python test_system.py")
    
    print(f"\n⚠️  Press Ctrl+C to stop all agents")
    
    try:
        # Wait for user interrupt
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print(f"\n🛑 Stopping all agents...")
        for script, process in processes:
            process.terminate()
            print(f"  Stopped {script}")
        print("✅ All agents stopped")

if __name__ == "__main__":
    main()
