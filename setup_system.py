#!/usr/bin/env python3
"""Setup script for the multi-agent system."""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print setup banner."""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                Multi-Agent System Setup                     ║
║              Agent Communication Protocol (ACP)             ║
╚══════════════════════════════════════════════════════════════╝
    """)

def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "logs",
        "agents",
        "acp", 
        "scripts"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ {directory}/")
    
    return True

def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing dependencies...")
    
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True, text=True)
        
        print("✅ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print(f"   Error output: {e.stderr}")
        return False

def create_env_file():
    """Create .env file with default configuration."""
    print("⚙️  Creating configuration...")
    
    env_content = """# Multi-Agent System Configuration

# OpenRouter API (optional - for real AI models)
OPENROUTER_API_KEY=your_api_key_here

# Logging Configuration
LOG_LEVEL=INFO

# Agent URLs (for distributed deployment)
TASK_GENERATION_AGENT_URL=http://localhost:8001
SCHEDULING_AGENT_URL=http://localhost:8002

# ACP Protocol Settings
ACP_VERSION=1.0
ACP_TIMEOUT=30
"""
    
    if not Path(".env").exists():
        with open(".env", "w") as f:
            f.write(env_content)
        print("✅ .env file created")
    else:
        print("✅ .env file already exists")
    
    return True

def make_scripts_executable():
    """Make shell scripts executable."""
    print("🔧 Setting script permissions...")
    
    scripts = [
        "scripts/deploy_agents.sh"
    ]
    
    for script in scripts:
        if Path(script).exists():
            try:
                os.chmod(script, 0o755)
                print(f"   ✅ {script}")
            except Exception as e:
                print(f"   ⚠️  {script}: {e}")
    
    return True

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing imports...")
    
    required_modules = [
        "fastapi",
        "uvicorn", 
        "httpx",
        "pydantic",
        "loguru"
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("   Try running: pip install -r requirements.txt")
        return False
    
    return True

def validate_system():
    """Validate the system setup."""
    print("🔍 Validating system setup...")
    
    required_files = [
        "client_agent.py",
        "agents/task_generation_agent.py",
        "agents/scheduling_agent.py",
        "agents/base_agent.py",
        "acp/protocol.py",
        "acp/calling_agent.py",
        "acp/message_types.py",
        "config.py",
        "logging_config.py",
        "examples.py",
        "test_system.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing files: {', '.join(missing_files)}")
        return False
    
    return True

def print_next_steps():
    """Print next steps for the user."""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                        Setup Complete!                      ║
╚══════════════════════════════════════════════════════════════╝

🚀 Next Steps:

1. Start the multi-agent system:
   ./scripts/deploy_agents.sh start
   
   OR
   
   python scripts/start_all_agents.py start

2. Test the system:
   python test_system.py
   
3. Run examples:
   python examples.py

4. Check system status:
   curl http://localhost:8000/health

5. Send a test request:
   curl -X POST http://localhost:8000/process-project \\
     -H "Content-Type: application/json" \\
     -d '{"project_description": "Build a web app", "request_type": "full"}'

📚 Documentation:
   - README.md: Complete system documentation
   - examples.py: Usage examples and patterns
   - test_system.py: Comprehensive test suite

🔧 Management Commands:
   - Start: ./scripts/deploy_agents.sh start
   - Stop:  ./scripts/deploy_agents.sh stop
   - Status: ./scripts/deploy_agents.sh status
   - Logs:  ./scripts/deploy_agents.sh logs

🌐 Agent URLs (when running):
   - Client Agent: http://localhost:8000
   - Task Generation Agent: http://localhost:8001
   - Scheduling Agent: http://localhost:8002

Happy coding! 🎉
    """)

def main():
    """Main setup function."""
    print_banner()
    
    steps = [
        ("Python Version", check_python_version),
        ("Directories", create_directories),
        ("Dependencies", install_dependencies),
        ("Configuration", create_env_file),
        ("Script Permissions", make_scripts_executable),
        ("Import Test", test_imports),
        ("System Validation", validate_system)
    ]
    
    print("🔧 Starting setup process...\n")
    
    for step_name, step_func in steps:
        print(f"{'='*60}")
        print(f"Step: {step_name}")
        print(f"{'='*60}")
        
        if not step_func():
            print(f"\n❌ Setup failed at step: {step_name}")
            print("   Please fix the issues above and run setup again.")
            sys.exit(1)
        
        print()
    
    print_next_steps()

if __name__ == "__main__":
    main()
