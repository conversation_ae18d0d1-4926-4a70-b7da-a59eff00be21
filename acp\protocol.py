"""ACP Protocol implementation for agent communication."""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import httpx
from loguru import logger

from .message_types import (
    ACPMessage, ACPRequest, ACPResponse, ACPError,
    MessageType, AgentInfo
)
from logging_config import ACPLoggingMixin

class ACPProtocol(ACPLoggingMixin):
    """Agent Communication Protocol implementation."""
    
    def __init__(self, agent_id: str, agent_info: AgentInfo):
        """Initialize ACP protocol.
        
        Args:
            agent_id: Unique identifier for this agent
            agent_info: Information about this agent
        """
        self.agent_id = agent_id
        self.agent_info = agent_info
        self.message_handlers: Dict[str, Callable] = {}
        self.pending_requests: Dict[str, asyncio.Future] = {}
        self.client = httpx.AsyncClient(timeout=30.0)
        
        logger.info(f"ACP Protocol initialized for agent {agent_id}")
    
    def register_handler(self, action: str, handler: Callable):
        """Register a message handler for a specific action.
        
        Args:
            action: Action name to handle
            handler: Async function to handle the action
        """
        self.message_handlers[action] = handler
        logger.info(f"Registered handler for action: {action}")
    
    async def send_request(
        self, 
        target_agent_url: str, 
        action: str, 
        payload: Dict[str, Any],
        timeout: int = 30
    ) -> Dict[str, Any]:
        """Send an ACP request to another agent.
        
        Args:
            target_agent_url: URL of target agent
            action: Action to request
            payload: Request payload
            timeout: Request timeout in seconds
            
        Returns:
            Response data from target agent
            
        Raises:
            Exception: If request fails or times out
        """
        message_id = str(uuid.uuid4())
        request = ACPRequest(
            message_id=message_id,
            sender_id=self.agent_id,
            receiver_id="unknown",  # Will be filled by receiver
            action=action,
            payload=payload,
            timeout=timeout
        )
        
        logger.info(f"Sending ACP request {message_id} to {target_agent_url}")
        logger.debug(f"Request payload: {payload}")

        # Log ACP request
        self.log_acp_request(target_agent_url, action, payload, message_id)

        try:
            response = await self.client.post(
                f"{target_agent_url}/acp/message",
                json=request.dict(),
                timeout=timeout
            )
            response.raise_for_status()
            
            response_data = response.json()
            logger.info(f"Received ACP response for request {message_id}")
            logger.debug(f"Response data: {response_data}")

            # Log ACP response
            success = response_data.get("success", False)
            result = response_data.get("result", {})
            error = response_data.get("error")
            self.log_acp_response(message_id, success, result, error)

            if not success:
                error_msg = error or "Unknown error"
                logger.error(f"ACP request failed: {error_msg}")
                raise Exception(f"ACP request failed: {error_msg}")

            return result
            
        except httpx.TimeoutException:
            logger.error(f"ACP request {message_id} timed out")
            raise Exception(f"Request timed out after {timeout} seconds")
        except httpx.HTTPStatusError as e:
            logger.error(f"ACP request {message_id} failed with status {e.response.status_code}")
            raise Exception(f"HTTP error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"ACP request {message_id} failed: {str(e)}")
            raise
    
    async def handle_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle incoming ACP message.
        
        Args:
            message_data: Raw message data
            
        Returns:
            Response data
        """
        try:
            # Parse message
            message = ACPRequest(**message_data)
            logger.info(f"Handling ACP message {message.message_id} from {message.sender_id}")
            logger.debug(f"Message action: {message.action}")
            
            # Check if we have a handler for this action
            if message.action not in self.message_handlers:
                error_msg = f"No handler registered for action: {message.action}"
                logger.error(error_msg)
                return self._create_error_response(message.message_id, "HANDLER_NOT_FOUND", error_msg)
            
            # Execute handler
            handler = self.message_handlers[message.action]
            result = await handler(message.payload)
            
            logger.info(f"Successfully handled message {message.message_id}")
            return self._create_success_response(message.message_id, result)
            
        except Exception as e:
            logger.error(f"Error handling ACP message: {str(e)}")
            message_id = message_data.get("message_id", "unknown")
            return self._create_error_response(message_id, "HANDLER_ERROR", str(e))
    
    def _create_success_response(self, request_id: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """Create a success response."""
        response = ACPResponse(
            message_id=str(uuid.uuid4()),
            sender_id=self.agent_id,
            receiver_id="client",
            request_id=request_id,
            success=True,
            result=result
        )
        return response.dict()
    
    def _create_error_response(self, request_id: str, error_code: str, error_message: str) -> Dict[str, Any]:
        """Create an error response."""
        response = ACPResponse(
            message_id=str(uuid.uuid4()),
            sender_id=self.agent_id,
            receiver_id="client",
            request_id=request_id,
            success=False,
            error=f"{error_code}: {error_message}"
        )
        return response.dict()
    
    async def close(self):
        """Close the protocol and cleanup resources."""
        await self.client.aclose()
        logger.info(f"ACP Protocol closed for agent {self.agent_id}")
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Get agent information."""
        return self.agent_info.dict()
