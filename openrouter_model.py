"""OpenRouter Model Implementation for ACPCallingAgent"""

import os
from typing import List, Dict, Any, Optional
import openai
from dotenv import load_dotenv
from fastacp import ChatMessage, ToolCall

# Load environment variables
load_dotenv()

class OpenRouterModel:
    """OpenRouter model implementation compatible with ACPCallingAgent"""
    
    def __init__(self, model_id: str = None, api_key: str = None):
        self.model_id = model_id or os.getenv("OPENROUTER_MODEL", "meta-llama/llama-3.3-70b-instruct:free")
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        
        if not self.api_key:
            raise ValueError("OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable.")
        
        # Configure OpenAI client for OpenRouter
        self.client = openai.OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.api_key,
        )
    
    def __call__(self, messages: List[Dict[str, Any]], tools_to_call_from: List = None, stop_sequences: List[str] = None) -> ChatMessage:
        """Call the OpenRouter model with messages and optional tools"""
        
        # Convert messages to OpenAI format
        openai_messages = []
        for msg in messages:
            if isinstance(msg.get("content"), list):
                # Handle new format with content as list
                content = ""
                for part in msg["content"]:
                    if part.get("type") == "text":
                        content += part.get("text", "")
                openai_messages.append({
                    "role": msg["role"],
                    "content": content
                })
            else:
                # Handle simple string content
                openai_messages.append({
                    "role": msg["role"], 
                    "content": msg.get("content", "")
                })
        
        # Prepare tools for OpenAI format
        tools = None
        if tools_to_call_from:
            tools = []
            for tool in tools_to_call_from:
                tool_def = {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    }
                }
                
                # Add parameters from tool inputs
                if hasattr(tool, 'inputs') and tool.inputs:
                    for param_name, param_info in tool.inputs.items():
                        if isinstance(param_info, dict):
                            tool_def["function"]["parameters"]["properties"][param_name] = {
                                "type": param_info.get("type", "string"),
                                "description": param_info.get("description", "")
                            }
                            tool_def["function"]["parameters"]["required"].append(param_name)
                        else:
                            tool_def["function"]["parameters"]["properties"][param_name] = {
                                "type": "string",
                                "description": str(param_info)
                            }
                            tool_def["function"]["parameters"]["required"].append(param_name)
                
                tools.append(tool_def)
        
        # Make API call
        try:
            kwargs = {
                "model": self.model_id,
                "messages": openai_messages,
                "max_tokens": 1000,
                "temperature": 0.7
            }
            
            if tools:
                kwargs["tools"] = tools
                kwargs["tool_choice"] = "auto"
            
            if stop_sequences:
                kwargs["stop"] = stop_sequences
            
            response = self.client.chat.completions.create(**kwargs)
            
            # Extract response
            message = response.choices[0].message
            content = message.content
            
            # Handle tool calls
            tool_calls = []
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    tool_calls.append(ToolCall(
                        name=tool_call.function.name,
                        arguments=tool_call.function.arguments,
                        id=tool_call.id
                    ))
            
            return ChatMessage(
                content=content,
                tool_calls=tool_calls if tool_calls else None,
                raw=response
            )
            
        except Exception as e:
            print(f"OpenRouter API Error: {str(e)}")
            # Return a fallback response
            return ChatMessage(
                content=f"I encountered an error: {str(e)}. Please try again.",
                tool_calls=None,
                raw=None
            )
