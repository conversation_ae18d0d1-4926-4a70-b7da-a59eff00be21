"""ACPCallingAgent implementation for routing and calling other agents."""

import asyncio
from typing import Dict, Any, List, Optional
from loguru import logger

from .protocol import ACPProtocol
from .message_types import AgentInfo, AgentCapability
from config import settings, AGENT_CONFIGS, REQUEST_TYPES

class ACPCallingAgent:
    """Agent that can call and route requests to other agents using ACP."""
    
    def __init__(self, agent_id: str = "client_agent"):
        """Initialize the calling agent.
        
        Args:
            agent_id: Unique identifier for this agent
        """
        self.agent_id = agent_id
        
        # Create agent info
        client_config = AGENT_CONFIGS["client"]
        self.agent_info = AgentInfo(
            agent_id=agent_id,
            name=client_config["name"],
            description=client_config["description"],
            capabilities=[AgentCapability.ROUTING],
            endpoint=f"http://localhost:{client_config['port']}",
            framework=client_config["framework"]
        )
        
        # Initialize ACP protocol
        self.acp = ACPProtocol(agent_id, self.agent_info)
        
        # Agent registry
        self.agent_registry: Dict[str, str] = {
            "task_generation": settings.TASK_GENERATION_AGENT_URL,
            "scheduling": settings.SCHEDULING_AGENT_URL
        }
        
        logger.info(f"ACPCallingAgent initialized: {agent_id}")
    
    async def process_project_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a project request by routing to appropriate agents.
        
        Args:
            request_data: Project request data
            
        Returns:
            Combined result from all agents
        """
        project_description = request_data.get("project_description", "")
        request_type = request_data.get("request_type", "full")
        
        logger.info(f"Processing project request: type={request_type}")
        logger.info(f"Project description: {project_description}")
        
        # Validate request type
        if request_type not in REQUEST_TYPES:
            raise ValueError(f"Invalid request type: {request_type}. Must be one of: {list(REQUEST_TYPES.keys())}")
        
        # Get workflow for request type
        workflow = REQUEST_TYPES[request_type]["workflow"]
        logger.info(f"Executing workflow: {workflow}")
        
        result = {
            "project_description": project_description,
            "request_type": request_type,
            "workflow_executed": workflow,
            "results": {}
        }
        
        # Execute workflow steps
        task_generation_result = None
        
        for step in workflow:
            if step == "task_generation":
                logger.info("Calling Task Generation Agent...")
                task_generation_result = await self._call_task_generation_agent(request_data)
                result["results"]["task_generation"] = task_generation_result
                
            elif step == "scheduling":
                if not task_generation_result:
                    raise ValueError("Cannot perform scheduling without task generation results")
                
                logger.info("Calling Scheduling Agent...")
                scheduling_data = {
                    "project_id": task_generation_result.get("project_id"),
                    "tasks": task_generation_result.get("tasks", []),
                    "project_description": project_description
                }
                scheduling_result = await self._call_scheduling_agent(scheduling_data)
                result["results"]["scheduling"] = scheduling_result
        
        logger.info("Project request processing completed")
        return result
    
    async def _call_task_generation_agent(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Call the task generation agent.
        
        Args:
            request_data: Request data for task generation
            
        Returns:
            Task generation result
        """
        agent_url = self.agent_registry["task_generation"]
        
        payload = {
            "project_description": request_data.get("project_description", ""),
            "requirements": request_data.get("requirements", []),
            "constraints": request_data.get("constraints", {}),
            "deadline": request_data.get("deadline")
        }
        
        logger.info(f"Sending request to Task Generation Agent at {agent_url}")
        logger.debug(f"Payload: {payload}")
        
        try:
            result = await self.acp.send_request(
                target_agent_url=agent_url,
                action="generate_tasks",
                payload=payload,
                timeout=60
            )
            
            logger.info("Task Generation Agent responded successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to call Task Generation Agent: {str(e)}")
            raise Exception(f"Task generation failed: {str(e)}")
    
    async def _call_scheduling_agent(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Call the scheduling agent.
        
        Args:
            request_data: Request data for scheduling
            
        Returns:
            Scheduling result
        """
        agent_url = self.agent_registry["scheduling"]
        
        payload = {
            "project_id": request_data.get("project_id"),
            "tasks": request_data.get("tasks", []),
            "project_description": request_data.get("project_description", ""),
            "constraints": request_data.get("constraints", {})
        }
        
        logger.info(f"Sending request to Scheduling Agent at {agent_url}")
        logger.debug(f"Payload: {payload}")
        
        try:
            result = await self.acp.send_request(
                target_agent_url=agent_url,
                action="create_schedule",
                payload=payload,
                timeout=60
            )
            
            logger.info("Scheduling Agent responded successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to call Scheduling Agent: {str(e)}")
            raise Exception(f"Scheduling failed: {str(e)}")
    
    async def get_agent_status(self, agent_name: str) -> Dict[str, Any]:
        """Get status of a specific agent.
        
        Args:
            agent_name: Name of the agent to check
            
        Returns:
            Agent status information
        """
        if agent_name not in self.agent_registry:
            raise ValueError(f"Unknown agent: {agent_name}")
        
        agent_url = self.agent_registry[agent_name]
        
        try:
            result = await self.acp.send_request(
                target_agent_url=agent_url,
                action="get_status",
                payload={},
                timeout=10
            )
            
            return {
                "agent_name": agent_name,
                "status": "active",
                "details": result
            }
            
        except Exception as e:
            logger.error(f"Failed to get status for agent {agent_name}: {str(e)}")
            return {
                "agent_name": agent_name,
                "status": "error",
                "error": str(e)
            }
    
    async def list_available_agents(self) -> List[Dict[str, Any]]:
        """List all available agents and their capabilities.
        
        Returns:
            List of agent information
        """
        agents = []
        
        for agent_name, agent_url in self.agent_registry.items():
            try:
                status = await self.get_agent_status(agent_name)
                config = AGENT_CONFIGS.get(agent_name, {})
                
                agents.append({
                    "name": agent_name,
                    "url": agent_url,
                    "capabilities": config.get("capabilities", []),
                    "framework": config.get("framework", "unknown"),
                    "status": status["status"]
                })
                
            except Exception as e:
                logger.error(f"Failed to get info for agent {agent_name}: {str(e)}")
                agents.append({
                    "name": agent_name,
                    "url": agent_url,
                    "status": "error",
                    "error": str(e)
                })
        
        return agents
    
    async def close(self):
        """Close the calling agent and cleanup resources."""
        await self.acp.close()
        logger.info("ACPCallingAgent closed")
