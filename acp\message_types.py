"""ACP message types and data structures."""

from typing import Any, Dict, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class MessageType(str, Enum):
    """ACP message types."""
    REQUEST = "request"
    RESPONSE = "response"
    ERROR = "error"
    HEARTBEAT = "heartbeat"

class AgentCapability(str, Enum):
    """Agent capabilities."""
    TASK_GENERATION = "task_generation"
    SCHEDULING = "scheduling"
    ROUTING = "routing"
    PROJECT_ANALYSIS = "project_analysis"
    TIME_PLANNING = "time_planning"
    RESOURCE_ALLOCATION = "resource_allocation"

class ACPMessage(BaseModel):
    """Base ACP message structure."""
    
    message_id: str = Field(..., description="Unique message identifier")
    message_type: MessageType = Field(..., description="Type of message")
    sender_id: str = Field(..., description="ID of sending agent")
    receiver_id: str = Field(..., description="ID of receiving agent")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    acp_version: str = Field(default="1.0", description="ACP protocol version")
    
    class Config:
        use_enum_values = True

class ACPRequest(ACPMessage):
    """ACP request message."""
    
    message_type: MessageType = Field(default=MessageType.REQUEST, const=True)
    action: str = Field(..., description="Requested action")
    payload: Dict[str, Any] = Field(default_factory=dict, description="Request payload")
    requires_response: bool = Field(default=True, description="Whether response is required")
    timeout: Optional[int] = Field(default=30, description="Request timeout in seconds")

class ACPResponse(ACPMessage):
    """ACP response message."""
    
    message_type: MessageType = Field(default=MessageType.RESPONSE, const=True)
    request_id: str = Field(..., description="ID of original request")
    success: bool = Field(..., description="Whether request was successful")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Response result")
    error: Optional[str] = Field(default=None, description="Error message if failed")

class ACPError(ACPMessage):
    """ACP error message."""
    
    message_type: MessageType = Field(default=MessageType.ERROR, const=True)
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Error description")
    original_request_id: Optional[str] = Field(default=None, description="ID of request that caused error")

class AgentInfo(BaseModel):
    """Agent information structure."""
    
    agent_id: str = Field(..., description="Unique agent identifier")
    name: str = Field(..., description="Agent name")
    description: str = Field(..., description="Agent description")
    capabilities: List[AgentCapability] = Field(..., description="Agent capabilities")
    endpoint: str = Field(..., description="Agent endpoint URL")
    status: str = Field(default="active", description="Agent status")
    framework: str = Field(..., description="Framework used by agent")

class TaskItem(BaseModel):
    """Individual task structure."""
    
    task_id: str = Field(..., description="Unique task identifier")
    title: str = Field(..., description="Task title")
    description: str = Field(..., description="Task description")
    priority: str = Field(default="medium", description="Task priority (low/medium/high)")
    estimated_duration: Optional[str] = Field(default=None, description="Estimated duration")
    dependencies: List[str] = Field(default_factory=list, description="Task dependencies")
    tags: List[str] = Field(default_factory=list, description="Task tags")

class ScheduleItem(BaseModel):
    """Individual schedule item structure."""
    
    schedule_id: str = Field(..., description="Unique schedule identifier")
    task_id: str = Field(..., description="Associated task ID")
    start_time: Optional[datetime] = Field(default=None, description="Scheduled start time")
    end_time: Optional[datetime] = Field(default=None, description="Scheduled end time")
    duration: str = Field(..., description="Duration estimate")
    resource_requirements: List[str] = Field(default_factory=list, description="Required resources")
    notes: Optional[str] = Field(default=None, description="Additional notes")

class ProjectRequest(BaseModel):
    """Project processing request structure."""
    
    project_description: str = Field(..., description="Description of the project")
    request_type: str = Field(..., description="Type of request (full/partial)")
    requirements: Optional[List[str]] = Field(default_factory=list, description="Specific requirements")
    constraints: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Project constraints")
    deadline: Optional[datetime] = Field(default=None, description="Project deadline")

class TaskGenerationResult(BaseModel):
    """Result from task generation agent."""
    
    project_id: str = Field(..., description="Generated project ID")
    tasks: List[TaskItem] = Field(..., description="Generated tasks")
    summary: str = Field(..., description="Task generation summary")
    total_estimated_duration: Optional[str] = Field(default=None, description="Total estimated duration")

class SchedulingResult(BaseModel):
    """Result from scheduling agent."""
    
    project_id: str = Field(..., description="Project ID")
    schedule: List[ScheduleItem] = Field(..., description="Generated schedule")
    timeline_summary: str = Field(..., description="Timeline summary")
    total_duration: str = Field(..., description="Total project duration")
    critical_path: List[str] = Field(default_factory=list, description="Critical path task IDs")
