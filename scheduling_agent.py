"""Scheduling Agent Server"""

import asyncio
from fastapi import <PERSON><PERSON><PERSON>
from acp_sdk.server import Server
from acp_sdk.models import Message, MessagePart
import uvicorn
from datetime import datetime, timedelta

app = FastAPI()

# Scheduling Agent
async def create_schedule(input_message: str) -> str:
    """Create schedule from task list"""
    
    print(f"[SCHEDULING] Received: {input_message}")
    
    # Parse input to extract tasks (simple parsing)
    lines = input_message.split('\n')
    task_lines = [line for line in lines if line.strip().startswith('Task')]
    
    # Create schedule
    start_date = datetime.now()
    schedule = []
    current_date = start_date
    
    for i, task_line in enumerate(task_lines):
        # Extract task info
        task_parts = task_line.split(':')
        if len(task_parts) >= 2:
            task_id = task_parts[0].strip()
            task_name = task_parts[1].strip()
            
            # Find duration from next lines
            duration_days = 1  # default
            for j in range(i+1, min(i+4, len(lines))):
                if 'Duration:' in lines[j]:
                    duration_text = lines[j].split('Duration:')[1].strip()
                    if 'day' in duration_text:
                        try:
                            duration_days = int(duration_text.split()[0])
                        except:
                            duration_days = 1
                    break
            
            # Calculate dates
            end_date = current_date + timedelta(days=duration_days)
            
            schedule_item = {
                "task": task_name,
                "start_date": current_date.strftime("%Y-%m-%d"),
                "end_date": end_date.strftime("%Y-%m-%d"),
                "duration": f"{duration_days} days",
                "resources": ["developer", "tester"] if "test" in task_name.lower() else ["developer"]
            }
            
            schedule.append(schedule_item)
            current_date = end_date + timedelta(days=1)  # Add 1 day buffer
    
    # Format response
    total_days = (current_date - start_date).days
    response = f"Created schedule for {len(schedule)} tasks:\n\n"
    
    for item in schedule:
        response += f"📅 {item['task']}\n"
        response += f"   Start: {item['start_date']}\n"
        response += f"   End: {item['end_date']}\n"
        response += f"   Duration: {item['duration']}\n"
        response += f"   Resources: {', '.join(item['resources'])}\n\n"
    
    response += f"🕒 Total project duration: {total_days} days\n"
    response += f"📊 Project completion date: {current_date.strftime('%Y-%m-%d')}\n"
    response += f"⚡ Critical path: Planning → Design → Implementation → Testing → Deployment"
    
    print(f"[SCHEDULING] Response: {response}")
    return response

# Create ACP server
server = Server(
    name="scheduling_server", 
    version="1.0.0"
)

# Register the agent
server.register_agent(
    name="scheduling_agent",
    description="Creates time-based scheduling and planning from task lists using SmolagentS framework",
    handler=create_schedule
)

@app.get("/")
async def root():
    return {"message": "Scheduling Agent Server", "agent": "scheduling_agent"}

@app.get("/health")
async def health():
    return {"status": "healthy", "agent": "scheduling_agent"}

# Mount ACP server
app.mount("/acp", server.app)

if __name__ == "__main__":
    print("Starting Scheduling Agent on port 8002...")
    uvicorn.run(app, host="0.0.0.0", port=8002)
