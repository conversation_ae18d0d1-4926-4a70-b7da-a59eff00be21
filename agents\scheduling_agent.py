"""Scheduling Agent using SmolagentS framework."""

import uuid
from typing import Dict, Any, List
from datetime import datetime, timedelta
from loguru import logger

from .base_agent import BaseAgent
from acp.message_types import AgentCapability, ScheduleItem, SchedulingResult, TaskItem
from config import AGENT_CONFIGS

# Mock SmolagentS implementation for demonstration
class MockSmolagentS:
    """Mock SmolagentS implementation for scheduling."""
    
    def __init__(self):
        self.agents = []
        self.tools = []
    
    def create_agent(self, name: str, description: str, tools: List[str] = None):
        """Create a SmolagentS agent."""
        agent = {
            "name": name,
            "description": description,
            "tools": tools or [],
            "id": str(uuid.uuid4())
        }
        self.agents.append(agent)
        return agent
    
    def add_tool(self, name: str, description: str, function):
        """Add a tool to the agent."""
        tool = {
            "name": name,
            "description": description,
            "function": function,
            "id": str(uuid.uuid4())
        }
        self.tools.append(tool)
        return tool
    
    def execute_scheduling(self, tasks: List[Dict[str, Any]], constraints: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute scheduling algorithm."""
        schedule = []
        current_date = datetime.now()
        
        # Sort tasks by priority
        priority_order = {"high": 0, "medium": 1, "low": 2}
        sorted_tasks = sorted(tasks, key=lambda x: priority_order.get(x.get("priority", "medium"), 1))
        
        for i, task in enumerate(sorted_tasks):
            # Calculate duration
            duration_str = task.get("estimated_duration", "1 day")
            if "day" in duration_str:
                duration_days = int(duration_str.split()[0])
                duration_hours = duration_days * 8
            elif "hour" in duration_str:
                duration_hours = int(duration_str.split()[0])
            else:
                duration_hours = 8  # Default to 1 day
            
            # Calculate start and end times
            start_time = current_date + timedelta(days=i * 0.5)  # Overlap tasks slightly
            end_time = start_time + timedelta(hours=duration_hours)
            
            schedule_item = {
                "schedule_id": str(uuid.uuid4()),
                "task_id": task.get("task_id"),
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration": duration_str,
                "resource_requirements": self._determine_resources(task),
                "notes": f"Scheduled based on {task.get('priority', 'medium')} priority"
            }
            
            schedule.append(schedule_item)
        
        return schedule
    
    def _determine_resources(self, task: Dict[str, Any]) -> List[str]:
        """Determine resource requirements for a task."""
        resources = ["developer"]
        
        tags = task.get("tags", [])
        if "frontend" in tags:
            resources.append("ui_designer")
        if "database" in tags:
            resources.append("database_admin")
        if "testing" in tags:
            resources.append("qa_engineer")
        if "security" in tags:
            resources.append("security_specialist")
        
        return resources

class SchedulingAgent(BaseAgent):
    """Agent specialized in creating schedules from task lists."""
    
    def __init__(self):
        """Initialize the Scheduling Agent."""
        config = AGENT_CONFIGS["scheduling"]
        
        super().__init__(
            agent_id="scheduling_agent",
            name=config["name"],
            description=config["description"],
            capabilities=[
                AgentCapability.SCHEDULING,
                AgentCapability.TIME_PLANNING,
                AgentCapability.RESOURCE_ALLOCATION
            ],
            port=config["port"],
            framework=config["framework"]
        )
        
        # Initialize SmolagentS (mock implementation)
        self.smolagents = MockSmolagentS()
        self._setup_agents()
        
        logger.info("Scheduling Agent initialized with SmolagentS")
    
    def _setup_agents(self):
        """Setup SmolagentS agents and tools."""
        # Create scheduling agent
        self.scheduler = self.smolagents.create_agent(
            name="Project Scheduler",
            description="Specialized in creating optimal project schedules",
            tools=["calendar", "resource_planner", "dependency_analyzer"]
        )
        
        # Create resource planner
        self.resource_planner = self.smolagents.create_agent(
            name="Resource Planner",
            description="Manages resource allocation and availability",
            tools=["resource_tracker", "capacity_planner"]
        )
        
        # Add scheduling tools
        self.smolagents.add_tool(
            name="calendar",
            description="Calendar management for scheduling",
            function=self._calendar_tool
        )
        
        self.smolagents.add_tool(
            name="resource_planner",
            description="Resource planning and allocation",
            function=self._resource_planning_tool
        )
        
        logger.info("SmolagentS agents and tools setup completed")
    
    def _calendar_tool(self, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calendar tool for scheduling."""
        return {"status": "scheduled", "tasks": len(tasks)}
    
    def _resource_planning_tool(self, resources: List[str]) -> Dict[str, Any]:
        """Resource planning tool."""
        return {"status": "planned", "resources": resources}
    
    def _register_custom_handlers(self):
        """Register custom ACP handlers."""
        self.acp.register_handler("create_schedule", self._handle_create_schedule)
    
    async def _handle_create_schedule(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle schedule creation requests."""
        self.log_request("create_schedule", payload)
        
        try:
            result = await self.process_request(payload)
            self.log_response("create_schedule", result)
            return result
        except Exception as e:
            logger.error(f"Schedule creation failed: {str(e)}")
            raise
    
    async def process_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Process schedule creation request.
        
        Args:
            payload: Request payload containing tasks and constraints
            
        Returns:
            Scheduling result
        """
        project_id = payload.get("project_id", "")
        tasks = payload.get("tasks", [])
        project_description = payload.get("project_description", "")
        constraints = payload.get("constraints", {})
        
        logger.info(f"Creating schedule for project {project_id} with {len(tasks)} tasks")
        
        # Use SmolagentS to create schedule
        schedule_items = await self._create_schedule_with_smolagents(
            tasks, constraints, project_description
        )
        
        # Convert to ScheduleItem objects
        schedule = []
        for item_data in schedule_items:
            schedule_item = ScheduleItem(**item_data)
            schedule.append(schedule_item)
        
        # Calculate project timeline
        timeline_summary, total_duration, critical_path = self._analyze_timeline(schedule, tasks)
        
        # Create result
        result = SchedulingResult(
            project_id=project_id,
            schedule=schedule,
            timeline_summary=timeline_summary,
            total_duration=total_duration,
            critical_path=critical_path
        )
        
        logger.info(f"Schedule creation completed: {len(schedule)} items scheduled")
        
        return result.dict()
    
    async def _create_schedule_with_smolagents(
        self, 
        tasks: List[Dict[str, Any]], 
        constraints: Dict[str, Any],
        project_description: str
    ) -> List[Dict[str, Any]]:
        """Create schedule using SmolagentS framework.
        
        Args:
            tasks: List of tasks to schedule
            constraints: Scheduling constraints
            project_description: Project description for context
            
        Returns:
            List of schedule items
        """
        logger.info("Executing SmolagentS scheduling...")
        
        # Execute scheduling with SmolagentS
        schedule_items = self.smolagents.execute_scheduling(tasks, constraints)
        
        # Apply constraints and optimizations
        optimized_schedule = self._optimize_schedule(schedule_items, constraints)
        
        logger.info(f"SmolagentS created schedule with {len(optimized_schedule)} items")
        
        return optimized_schedule
    
    def _optimize_schedule(
        self, 
        schedule_items: List[Dict[str, Any]], 
        constraints: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Optimize schedule based on constraints.
        
        Args:
            schedule_items: Initial schedule items
            constraints: Optimization constraints
            
        Returns:
            Optimized schedule
        """
        optimized = []
        
        for item in schedule_items:
            # Apply budget constraints
            if constraints.get("budget") == "low":
                # Reduce parallel work to minimize resource costs
                resources = item.get("resource_requirements", [])
                if len(resources) > 2:
                    item["resource_requirements"] = resources[:2]
                    item["notes"] += " (Resources reduced due to budget constraints)"
            
            # Apply timeline constraints
            if constraints.get("timeline") == "tight":
                # Increase parallelization where possible
                if "high" in item.get("notes", ""):
                    # Add more resources for high priority tasks
                    resources = item.get("resource_requirements", [])
                    if "developer" in resources and len(resources) < 3:
                        resources.append("senior_developer")
                        item["resource_requirements"] = resources
                        item["notes"] += " (Resources increased for tight timeline)"
            
            optimized.append(item)
        
        return optimized
    
    def _analyze_timeline(
        self, 
        schedule: List[ScheduleItem], 
        tasks: List[Dict[str, Any]]
    ) -> tuple[str, str, List[str]]:
        """Analyze project timeline and identify critical path.
        
        Args:
            schedule: Schedule items
            tasks: Original tasks
            
        Returns:
            Tuple of (timeline_summary, total_duration, critical_path)
        """
        if not schedule:
            return "No schedule items", "0 days", []
        
        # Calculate total duration
        start_times = [datetime.fromisoformat(item.start_time.replace('Z', '+00:00')) for item in schedule]
        end_times = [datetime.fromisoformat(item.end_time.replace('Z', '+00:00')) for item in schedule]
        
        project_start = min(start_times)
        project_end = max(end_times)
        total_duration_days = (project_end - project_start).days + 1
        
        # Identify critical path (simplified - tasks with high priority)
        critical_path = []
        for item in schedule:
            # Find corresponding task
            task = next((t for t in tasks if t.get("task_id") == item.task_id), None)
            if task and task.get("priority") == "high":
                critical_path.append(item.task_id)
        
        # Create timeline summary
        timeline_summary = (
            f"Project spans {total_duration_days} days from "
            f"{project_start.strftime('%Y-%m-%d')} to {project_end.strftime('%Y-%m-%d')}. "
            f"Critical path includes {len(critical_path)} high-priority tasks."
        )
        
        return timeline_summary, f"{total_duration_days} days", critical_path
    
    def _calculate_resource_utilization(self, schedule: List[ScheduleItem]) -> Dict[str, Any]:
        """Calculate resource utilization statistics.
        
        Args:
            schedule: Schedule items
            
        Returns:
            Resource utilization data
        """
        resource_usage = {}
        
        for item in schedule:
            for resource in item.resource_requirements:
                if resource not in resource_usage:
                    resource_usage[resource] = 0
                
                # Calculate hours for this resource
                start_time = datetime.fromisoformat(item.start_time.replace('Z', '+00:00'))
                end_time = datetime.fromisoformat(item.end_time.replace('Z', '+00:00'))
                hours = (end_time - start_time).total_seconds() / 3600
                resource_usage[resource] += hours
        
        return {
            "resource_usage": resource_usage,
            "total_resources": len(resource_usage),
            "peak_utilization": max(resource_usage.values()) if resource_usage else 0
        }

def main():
    """Main function to run the Scheduling Agent."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Scheduling Agent")
    parser.add_argument("--port", type=int, default=8002, help="Port to run the agent on")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host to bind to")
    
    args = parser.parse_args()
    
    # Setup logging
    logger.add("logs/scheduling_agent.log", rotation="1 day", retention="7 days")
    
    # Create and run agent
    agent = SchedulingAgent()
    agent.run(host=args.host)

if __name__ == "__main__":
    main()
