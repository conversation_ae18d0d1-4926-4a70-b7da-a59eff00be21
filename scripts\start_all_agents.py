#!/usr/bin/env python3
"""Script to start all agents in the multi-agent system."""

import os
import sys
import time
import subprocess
import signal
import argparse
from typing import List, Dict
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from config import AGENT_CONFIGS, settings

class AgentManager:
    """Manager for starting and stopping agents."""
    
    def __init__(self):
        self.processes: Dict[str, subprocess.Popen] = {}
        self.agent_configs = AGENT_CONFIGS
        
    def start_agent(self, agent_name: str, host: str = "0.0.0.0") -> bool:
        """Start a specific agent.
        
        Args:
            agent_name: Name of the agent to start
            host: Host to bind to
            
        Returns:
            True if started successfully, False otherwise
        """
        if agent_name in self.processes:
            print(f"Agent {agent_name} is already running")
            return True
        
        config = self.agent_configs.get(agent_name)
        if not config:
            print(f"Unknown agent: {agent_name}")
            return False
        
        port = config["port"]
        
        # Determine the script to run
        if agent_name == "client":
            script_path = "client_agent.py"
        else:
            script_path = f"agents/{agent_name}_agent.py"
        
        # Check if script exists
        if not os.path.exists(script_path):
            print(f"Script not found: {script_path}")
            return False
        
        # Start the agent process
        cmd = [
            sys.executable,
            script_path,
            "--host", host,
            "--port", str(port)
        ]
        
        print(f"Starting {config['name']} on {host}:{port}...")
        print(f"Command: {' '.join(cmd)}")
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes[agent_name] = process
            
            # Give the process a moment to start
            time.sleep(2)
            
            # Check if process is still running
            if process.poll() is None:
                print(f"✓ {config['name']} started successfully (PID: {process.pid})")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"✗ {config['name']} failed to start")
                print(f"STDOUT: {stdout}")
                print(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            print(f"✗ Failed to start {config['name']}: {str(e)}")
            return False
    
    def stop_agent(self, agent_name: str) -> bool:
        """Stop a specific agent.
        
        Args:
            agent_name: Name of the agent to stop
            
        Returns:
            True if stopped successfully, False otherwise
        """
        if agent_name not in self.processes:
            print(f"Agent {agent_name} is not running")
            return True
        
        process = self.processes[agent_name]
        config = self.agent_configs.get(agent_name, {})
        
        print(f"Stopping {config.get('name', agent_name)}...")
        
        try:
            # Try graceful shutdown first
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout=10)
                print(f"✓ {config.get('name', agent_name)} stopped gracefully")
            except subprocess.TimeoutExpired:
                # Force kill if graceful shutdown fails
                process.kill()
                process.wait()
                print(f"✓ {config.get('name', agent_name)} force stopped")
            
            del self.processes[agent_name]
            return True
            
        except Exception as e:
            print(f"✗ Failed to stop {config.get('name', agent_name)}: {str(e)}")
            return False
    
    def start_all_agents(self, host: str = "0.0.0.0", exclude: List[str] = None) -> bool:
        """Start all agents in the correct order.
        
        Args:
            host: Host to bind to
            exclude: List of agent names to exclude
            
        Returns:
            True if all agents started successfully, False otherwise
        """
        exclude = exclude or []
        
        # Start order: specialized agents first, then client
        start_order = ["task_generation", "scheduling", "client"]
        
        success = True
        
        for agent_name in start_order:
            if agent_name in exclude:
                print(f"Skipping {agent_name} (excluded)")
                continue
            
            if not self.start_agent(agent_name, host):
                success = False
                break
            
            # Wait between starts to avoid port conflicts
            time.sleep(1)
        
        return success
    
    def stop_all_agents(self) -> bool:
        """Stop all running agents.
        
        Returns:
            True if all agents stopped successfully, False otherwise
        """
        # Stop in reverse order
        agent_names = list(self.processes.keys())
        agent_names.reverse()
        
        success = True
        
        for agent_name in agent_names:
            if not self.stop_agent(agent_name):
                success = False
        
        return success
    
    def status(self) -> Dict[str, str]:
        """Get status of all agents.
        
        Returns:
            Dictionary mapping agent names to their status
        """
        status = {}
        
        for agent_name, config in self.agent_configs.items():
            if agent_name in self.processes:
                process = self.processes[agent_name]
                if process.poll() is None:
                    status[agent_name] = f"running (PID: {process.pid})"
                else:
                    status[agent_name] = "stopped (process exited)"
                    # Clean up dead process
                    del self.processes[agent_name]
            else:
                status[agent_name] = "not running"
        
        return status
    
    def monitor_agents(self, interval: int = 5):
        """Monitor agents and restart if they crash.
        
        Args:
            interval: Check interval in seconds
        """
        print(f"Monitoring agents (checking every {interval}s)...")
        print("Press Ctrl+C to stop monitoring")
        
        try:
            while True:
                status = self.status()
                
                # Check for crashed agents
                for agent_name, agent_status in status.items():
                    if "stopped (process exited)" in agent_status:
                        print(f"Agent {agent_name} crashed, restarting...")
                        self.start_agent(agent_name)
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\nStopping monitoring...")
    
    def cleanup(self):
        """Cleanup all processes on exit."""
        print("Cleaning up processes...")
        self.stop_all_agents()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Multi-Agent System Manager")
    parser.add_argument("action", choices=["start", "stop", "restart", "status", "monitor"], 
                       help="Action to perform")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind agents to")
    parser.add_argument("--exclude", nargs="*", default=[], help="Agents to exclude")
    parser.add_argument("--agent", help="Specific agent to start/stop")
    parser.add_argument("--monitor-interval", type=int, default=5, 
                       help="Monitoring interval in seconds")
    
    args = parser.parse_args()
    
    manager = AgentManager()
    
    # Setup signal handlers for cleanup
    def signal_handler(sig, frame):
        print("\nReceived interrupt signal...")
        manager.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if args.action == "start":
            if args.agent:
                success = manager.start_agent(args.agent, args.host)
            else:
                success = manager.start_all_agents(args.host, args.exclude)
            
            if success:
                print("\n✓ All agents started successfully")
                print("\nAgent URLs:")
                for agent_name, config in AGENT_CONFIGS.items():
                    if agent_name not in args.exclude:
                        print(f"  {config['name']}: http://{args.host}:{config['port']}")
            else:
                print("\n✗ Some agents failed to start")
                sys.exit(1)
        
        elif args.action == "stop":
            if args.agent:
                success = manager.stop_agent(args.agent)
            else:
                success = manager.stop_all_agents()
            
            if success:
                print("\n✓ All agents stopped successfully")
            else:
                print("\n✗ Some agents failed to stop")
                sys.exit(1)
        
        elif args.action == "restart":
            print("Restarting agents...")
            manager.stop_all_agents()
            time.sleep(2)
            success = manager.start_all_agents(args.host, args.exclude)
            
            if success:
                print("\n✓ All agents restarted successfully")
            else:
                print("\n✗ Some agents failed to restart")
                sys.exit(1)
        
        elif args.action == "status":
            status = manager.status()
            print("\nAgent Status:")
            for agent_name, agent_status in status.items():
                config = AGENT_CONFIGS.get(agent_name, {})
                print(f"  {config.get('name', agent_name)}: {agent_status}")
        
        elif args.action == "monitor":
            manager.monitor_agents(args.monitor_interval)
    
    except Exception as e:
        print(f"Error: {str(e)}")
        manager.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
