from typing import List, Dict, Callable, Optional, Union, Any
import json
from dataclasses import dataclass
from enum import Enum
from colorama import Fore
from acp_sdk.client import Client
from acp_sdk.models import Message, MessagePart

# === AgentCollection Implementation ===
class Agent:
    """Representation of an ACP Agent."""
    def __init__(self, name: str, description: str, capabilities: List[str]):
        self.name = name
        self.description = description
        self.capabilities = capabilities

    def __str__(self):
        return f"Agent(name='{self.name}', description='{self.description}')"

class AgentCollection:
    """A collection of agents available on ACP servers."""
    
    def __init__(self):
        self.agents = []

    @classmethod
    async def from_acp(cls, *servers) -> 'AgentCollection':
        """Creates an AgentCollection by fetching agents from the provided ACP servers."""
        collection = cls()
        for server in servers:
            async for agent in server.agents():
                collection.agents.append((server, agent))
        return collection

    def get_agent(self, name: str) -> Optional[Agent]:
        """Find an agent by name in the collection."""
        for agent in self.agents:
            if agent.name == name:
                return agent
        return None

    def __iter__(self):
        """Allows iteration over all agents in the collection."""
        return iter(self.agents)

# === ACPCallingAgent Implementation ===
@dataclass
class ToolCall:
    """Represents a tool call with name, arguments, and optional ID."""
    name: str
    arguments: Union[Dict[str, Any], str]
    id: Optional[str] = None

@dataclass
class ChatMessage:
    """Represents a chat message with content and optional tool calls."""
    content: Optional[str]
    tool_calls: Optional[List[ToolCall]] = None
    raw: Any = None

class LogLevel(Enum):
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"

class Logger:
    """Simple logger for agent operations."""
    def log(self, content, level=LogLevel.INFO):
        print(f"[{level.value.upper()}] {content}")

    def log_markdown(self, content, title=None, level=LogLevel.INFO):
        if title:
            print(f"[{level.value.upper()}] {title}")
        print(f"[{level.value.upper()}] {content}")

class Tool:
    """Base class for tools that agents can use."""
    def __init__(self, name, description, inputs, output_type, client=None):
        self.name = name
        self.description = description
        self.inputs = inputs
        self.output_type = output_type
        self.client = client

    async def __call__(self, *args, **kwargs):
        print(Fore.YELLOW + 'Tool being called with args: ' + str(args) + ' and kwargs: ' + str(kwargs) + Fore.RESET)
        
        # Extract the input content
        content = ""
        if args and isinstance(args[0], str):
            content = args[0]
        elif "prompt" in kwargs:
            content = kwargs["prompt"]
        elif "input" in kwargs:
            content = kwargs["input"]
        elif kwargs:
            content = next(iter(kwargs.values()))

        print(Fore.MAGENTA + content + Fore.RESET)
        
        response = await self.client.run_sync(
            agent=self.name,
            input=[Message(parts=[MessagePart(content=content, content_type="text/plain")])]
        )
        
        print(Fore.RED + str(response) + Fore.RESET)
        return response.output[0].parts[0].content

class ACPCallingAgent:
    """Agent that uses ACP agent calls to delegate tasks to remote agents."""
    
    def __init__(
        self,
        acp_agents: Dict[str, Any],
        model: Callable,
        prompt_templates: Optional[Dict[str, str]] = None,
        **kwargs,
    ):
        # Default prompt templates
        if prompt_templates is None:
            prompt_templates = {
                "system_prompt": """You are a supervisory agent that can delegate tasks to specialized ACP agents.

Available agents:
{agents}

Your task is to:
1. Analyze the user's request
2. Call the appropriate agent(s) to gather information
3. When you have a complete answer, ALWAYS call the final_answer tool with your response
4. Do not provide answers directly in your messages - always use the final_answer tool
5. If you have sufficient information to complete a task do not call out to another agent unless required

Remember:
- Always use the final_answer tool when you have a complete answer
- Do not provide answers in your regular messages
- Chain multiple agent calls if needed to gather all required information
- The final_answer tool is the only way to return results to the user"""
            }

        # Convert ACP agents to tools
        self.tools = {}
        for name, agent in acp_agents.items():
            self.tools[name] = Tool(
                name=name,
                description=agent['agent'].description,
                inputs={"input": {"type": "string", "description": "the prompt to pass to the agent"}},
                output_type="str",
                client=agent['client']
            )

        # Add final_answer tool
        self.tools["final_answer"] = Tool(
            name="final_answer",
            description="Provide the final answer to the user's request",
            inputs={"answer": "The final answer to provide to the user"},
            output_type="str"
        )

        async def final_answer(answer, **kwargs):
            return answer

        self.tools["final_answer"].__call__ = final_answer

        self.acp_agents = acp_agents
        self.model = model
        self.prompt_templates = prompt_templates
        self.logger = Logger()
        self.state = {}
        self.input_messages = []

    def initialize_system_prompt(self) -> str:
        """Generate the system prompt for the agent with ACP agent information."""
        agent_descriptions = "\n".join(
            [f"- {name}: {agent['agent'].description}" for name, agent in self.acp_agents.items()]
        )
        
        system_prompt = self.prompt_templates["system_prompt"].format(agents=agent_descriptions)
        return system_prompt

    async def run(self, query: str, max_steps: int = 10) -> str:
        """Run the agent to completion with a user query."""
        
        # Initialize messages
        system_message = {"role": "system", "content": self.initialize_system_prompt()}
        user_message = {"role": "user", "content": query}
        self.input_messages = [system_message, user_message]

        # Run steps until we get a final answer
        for step_num in range(max_steps):
            self.logger.log(f"Step {step_num + 1}/{max_steps}", level=LogLevel.INFO)
            
            try:
                # Get response from model
                model_message = self.model(
                    self.input_messages,
                    tools_to_call_from=list(self.tools.values())[:-1],  # Exclude final_answer from auto-calls
                    stop_sequences=["Observation:", "Calling agents:"],
                )

                self.logger.log_markdown(
                    content=model_message.content if model_message.content else str(model_message.raw),
                    title="Output message of the LLM:",
                    level=LogLevel.DEBUG,
                )

                # Check if model called any tools
                if hasattr(model_message, 'tool_calls') and model_message.tool_calls:
                    tool_call = model_message.tool_calls[0]
                    
                    # Extract tool call details
                    if hasattr(tool_call, 'function'):
                        agent_name = tool_call.function.name
                        agent_arguments = tool_call.function.arguments
                    else:
                        agent_name = tool_call.name
                        agent_arguments = getattr(tool_call, 'arguments', {})

                    self.logger.log(f"Calling agent: '{agent_name}' with arguments: {agent_arguments}", level=LogLevel.INFO)

                    # Handle final_answer
                    if agent_name == "final_answer":
                        if isinstance(agent_arguments, dict):
                            answer = agent_arguments.get("answer", agent_arguments)
                        else:
                            answer = agent_arguments
                        
                        self.logger.log(f"Final answer: {answer}", level=LogLevel.INFO)
                        return str(answer)
                    
                    # Execute tool call
                    else:
                        tool = self.tools[agent_name]
                        if isinstance(agent_arguments, dict):
                            observation = await tool(**agent_arguments)
                        else:
                            observation = await tool(agent_arguments)
                        
                        self.logger.log(f"Observations: {observation}", level=LogLevel.INFO)
                        
                        # Add to conversation
                        self.input_messages.append({"role": "assistant", "content": model_message.content or ""})
                        self.input_messages.append({"role": "user", "content": f"Observation: {observation}"})

                else:
                    # No tool calls - check if content contains final answer
                    if model_message.content and "final_answer" in model_message.content.lower():
                        return model_message.content
                    else:
                        # Add message and continue
                        self.input_messages.append({"role": "assistant", "content": model_message.content or ""})
                        self.input_messages.append({"role": "user", "content": "Please provide a final answer using the final_answer tool."})

            except Exception as e:
                self.logger.log(f"Error in step {step_num + 1}: {str(e)}", level=LogLevel.ERROR)
                self.input_messages.append({
                    "role": "user", 
                    "content": f"Error occurred: {str(e)}. Please try a different approach or provide a final answer."
                })

        return "I wasn't able to complete this task within the maximum number of steps."
