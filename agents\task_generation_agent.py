"""Task Generation Agent using CrewAI framework."""

import uuid
from typing import Dict, Any, List
from datetime import datetime
from loguru import logger

from .base_agent import BaseAgent
from acp.message_types import AgentCapability, TaskItem, TaskGenerationResult
from config import AGENT_CONFIGS

# Mock CrewAI implementation for demonstration
class MockCrewAI:
    """Mock CrewAI implementation for task generation."""
    
    def __init__(self):
        self.agents = []
        self.tasks = []
    
    def create_agent(self, role: str, goal: str, backstory: str):
        """Create a CrewAI agent."""
        agent = {
            "role": role,
            "goal": goal,
            "backstory": backstory,
            "id": str(uuid.uuid4())
        }
        self.agents.append(agent)
        return agent
    
    def create_task(self, description: str, agent: Dict[str, Any]):
        """Create a CrewAI task."""
        task = {
            "description": description,
            "agent": agent,
            "id": str(uuid.uuid4())
        }
        self.tasks.append(task)
        return task
    
    def execute_crew(self) -> List[Dict[str, Any]]:
        """Execute the crew and return results."""
        # Mock task generation based on project description
        return [
            {
                "task_id": str(uuid.uuid4()),
                "title": "Project Planning",
                "description": "Create detailed project plan and requirements analysis",
                "priority": "high",
                "estimated_duration": "2 days",
                "dependencies": [],
                "tags": ["planning", "analysis"]
            },
            {
                "task_id": str(uuid.uuid4()),
                "title": "System Design",
                "description": "Design system architecture and technical specifications",
                "priority": "high",
                "estimated_duration": "3 days",
                "dependencies": [],
                "tags": ["design", "architecture"]
            },
            {
                "task_id": str(uuid.uuid4()),
                "title": "Implementation",
                "description": "Implement core functionality and features",
                "priority": "medium",
                "estimated_duration": "5 days",
                "dependencies": [],
                "tags": ["development", "coding"]
            },
            {
                "task_id": str(uuid.uuid4()),
                "title": "Testing",
                "description": "Comprehensive testing and quality assurance",
                "priority": "medium",
                "estimated_duration": "2 days",
                "dependencies": [],
                "tags": ["testing", "qa"]
            },
            {
                "task_id": str(uuid.uuid4()),
                "title": "Deployment",
                "description": "Deploy to production environment",
                "priority": "low",
                "estimated_duration": "1 day",
                "dependencies": [],
                "tags": ["deployment", "production"]
            }
        ]

class TaskGenerationAgent(BaseAgent):
    """Agent specialized in generating task lists from project descriptions."""
    
    def __init__(self):
        """Initialize the Task Generation Agent."""
        config = AGENT_CONFIGS["task_generation"]
        
        super().__init__(
            agent_id="task_generation_agent",
            name=config["name"],
            description=config["description"],
            capabilities=[
                AgentCapability.TASK_GENERATION,
                AgentCapability.PROJECT_ANALYSIS
            ],
            port=config["port"],
            framework=config["framework"]
        )
        
        # Initialize CrewAI (mock implementation)
        self.crew = MockCrewAI()
        self._setup_crew()
        
        logger.info("Task Generation Agent initialized with CrewAI")
    
    def _setup_crew(self):
        """Setup CrewAI agents and tasks."""
        # Create specialized agents for task generation
        self.project_analyst = self.crew.create_agent(
            role="Project Analyst",
            goal="Analyze project requirements and break them down into manageable tasks",
            backstory="Expert in project management and requirement analysis with 10+ years experience"
        )
        
        self.task_planner = self.crew.create_agent(
            role="Task Planner",
            goal="Create detailed task lists with priorities and dependencies",
            backstory="Specialized in task planning and workflow optimization"
        )
        
        logger.info("CrewAI agents setup completed")
    
    def _register_custom_handlers(self):
        """Register custom ACP handlers."""
        self.acp.register_handler("generate_tasks", self._handle_generate_tasks)
    
    async def _handle_generate_tasks(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle task generation requests."""
        self.log_request("generate_tasks", payload)
        
        try:
            result = await self.process_request(payload)
            self.log_response("generate_tasks", result)
            return result
        except Exception as e:
            logger.error(f"Task generation failed: {str(e)}")
            raise
    
    async def process_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Process task generation request.
        
        Args:
            payload: Request payload containing project details
            
        Returns:
            Task generation result
        """
        project_description = payload.get("project_description", "")
        requirements = payload.get("requirements", [])
        constraints = payload.get("constraints", {})
        deadline = payload.get("deadline")
        
        logger.info(f"Generating tasks for project: {project_description[:100]}...")
        
        # Generate project ID
        project_id = self.generate_id()
        
        # Use CrewAI to generate tasks
        raw_tasks = await self._generate_tasks_with_crew(
            project_description, requirements, constraints, deadline
        )
        
        # Convert to TaskItem objects
        tasks = []
        total_duration_hours = 0
        
        for task_data in raw_tasks:
            task = TaskItem(**task_data)
            tasks.append(task)
            
            # Calculate duration (simplified)
            duration_str = task.estimated_duration or "1 day"
            if "day" in duration_str:
                days = int(duration_str.split()[0])
                total_duration_hours += days * 8
            elif "hour" in duration_str:
                hours = int(duration_str.split()[0])
                total_duration_hours += hours
        
        # Create result
        result = TaskGenerationResult(
            project_id=project_id,
            tasks=tasks,
            summary=f"Generated {len(tasks)} tasks for the project",
            total_estimated_duration=f"{total_duration_hours} hours ({total_duration_hours // 8} days)"
        )
        
        logger.info(f"Task generation completed: {len(tasks)} tasks generated")
        
        return result.dict()
    
    async def _generate_tasks_with_crew(
        self, 
        project_description: str, 
        requirements: List[str], 
        constraints: Dict[str, Any],
        deadline: str = None
    ) -> List[Dict[str, Any]]:
        """Generate tasks using CrewAI framework.
        
        Args:
            project_description: Description of the project
            requirements: List of specific requirements
            constraints: Project constraints
            deadline: Project deadline
            
        Returns:
            List of generated tasks
        """
        logger.info("Executing CrewAI task generation...")
        
        # Create analysis task
        analysis_task = self.crew.create_task(
            description=f"Analyze the following project and break it down into tasks: {project_description}",
            agent=self.project_analyst
        )
        
        # Create planning task
        planning_task = self.crew.create_task(
            description="Create detailed task list with priorities and dependencies",
            agent=self.task_planner
        )
        
        # Execute crew (mock implementation)
        tasks = self.crew.execute_crew()
        
        # Enhance tasks based on requirements and constraints
        enhanced_tasks = self._enhance_tasks(tasks, requirements, constraints, deadline)
        
        logger.info(f"CrewAI generated {len(enhanced_tasks)} tasks")
        
        return enhanced_tasks
    
    def _enhance_tasks(
        self, 
        tasks: List[Dict[str, Any]], 
        requirements: List[str], 
        constraints: Dict[str, Any],
        deadline: str = None
    ) -> List[Dict[str, Any]]:
        """Enhance generated tasks with additional requirements and constraints.
        
        Args:
            tasks: Base tasks from CrewAI
            requirements: Additional requirements
            constraints: Project constraints
            deadline: Project deadline
            
        Returns:
            Enhanced task list
        """
        enhanced_tasks = []
        
        for task in tasks:
            # Add requirement-specific tasks
            if requirements:
                for req in requirements:
                    if any(keyword in req.lower() for keyword in ["security", "auth", "login"]):
                        task["tags"].append("security")
                    elif any(keyword in req.lower() for keyword in ["database", "data", "storage"]):
                        task["tags"].append("database")
                    elif any(keyword in req.lower() for keyword in ["ui", "interface", "frontend"]):
                        task["tags"].append("frontend")
            
            # Adjust priorities based on constraints
            if constraints.get("budget") == "low":
                if task["priority"] == "low":
                    task["priority"] = "medium"
            
            if deadline:
                # Increase priorities if deadline is tight
                if task["priority"] == "low":
                    task["priority"] = "medium"
            
            enhanced_tasks.append(task)
        
        # Add requirement-specific tasks
        if any("testing" in req.lower() for req in requirements):
            enhanced_tasks.append({
                "task_id": str(uuid.uuid4()),
                "title": "Additional Testing",
                "description": "Extended testing based on specific requirements",
                "priority": "medium",
                "estimated_duration": "1 day",
                "dependencies": [],
                "tags": ["testing", "requirements"]
            })
        
        return enhanced_tasks

def main():
    """Main function to run the Task Generation Agent."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Task Generation Agent")
    parser.add_argument("--port", type=int, default=8001, help="Port to run the agent on")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host to bind to")
    
    args = parser.parse_args()
    
    # Setup logging
    logger.add("logs/task_generation_agent.log", rotation="1 day", retention="7 days")
    
    # Create and run agent
    agent = TaskGenerationAgent()
    agent.run(host=args.host)

if __name__ == "__main__":
    main()
