"""Task Generation Agent Server"""

import asyncio
from fastapi import FastAP<PERSON>
from acp_sdk.server import Server
from acp_sdk.models import Message, MessagePart
import uvicorn

app = FastAPI()

# Task Generation Agent
async def generate_tasks(input_message: str) -> str:
    """Generate tasks from project description"""
    
    print(f"[TASK GENERATION] Received: {input_message}")
    
    # Simple task generation logic
    tasks = [
        {
            "id": 1,
            "title": "Project Planning",
            "description": "Create detailed project plan and requirements analysis",
            "priority": "high",
            "estimated_duration": "2 days"
        },
        {
            "id": 2,
            "title": "System Design", 
            "description": "Design system architecture and technical specifications",
            "priority": "high",
            "estimated_duration": "3 days"
        },
        {
            "id": 3,
            "title": "Implementation",
            "description": "Implement core functionality and features",
            "priority": "medium", 
            "estimated_duration": "5 days"
        },
        {
            "id": 4,
            "title": "Testing",
            "description": "Comprehensive testing and quality assurance",
            "priority": "medium",
            "estimated_duration": "2 days"
        },
        {
            "id": 5,
            "title": "Deployment",
            "description": "Deploy to production environment",
            "priority": "low",
            "estimated_duration": "1 day"
        }
    ]
    
    # Format response
    response = f"Generated {len(tasks)} tasks for project: {input_message}\n\n"
    for task in tasks:
        response += f"Task {task['id']}: {task['title']}\n"
        response += f"  Description: {task['description']}\n"
        response += f"  Priority: {task['priority']}\n"
        response += f"  Duration: {task['estimated_duration']}\n\n"
    
    response += f"Total estimated duration: 13 days"
    
    print(f"[TASK GENERATION] Response: {response}")
    return response

# Create ACP server
server = Server(
    name="task_generation_server",
    version="1.0.0"
)

# Register the agent
server.register_agent(
    name="task_agent",
    description="Generates comprehensive task lists from project descriptions using CrewAI framework",
    handler=generate_tasks
)

@app.get("/")
async def root():
    return {"message": "Task Generation Agent Server", "agent": "task_agent"}

@app.get("/health")
async def health():
    return {"status": "healthy", "agent": "task_agent"}

# Mount ACP server
app.mount("/acp", server.app)

if __name__ == "__main__":
    print("Starting Task Generation Agent on port 8001...")
    uvicorn.run(app, host="0.0.0.0", port=8001)
