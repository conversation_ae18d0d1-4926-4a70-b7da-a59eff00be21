#!/usr/bin/env python3
"""Interactive demonstration of the multi-agent system."""

import asyncio
import json
import time
from typing import Dict, Any
import httpx
from datetime import datetime

class MultiAgentDemo:
    """Interactive demonstration of the multi-agent system capabilities."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=120.0)
    
    def print_header(self, title: str):
        """Print a formatted header."""
        print(f"\n{'='*80}")
        print(f"🤖 {title}")
        print(f"{'='*80}")
    
    def print_step(self, step: str):
        """Print a step indicator."""
        print(f"\n📋 {step}")
        print("-" * 60)
    
    async def check_system_health(self):
        """Check if all agents are healthy."""
        self.print_step("Checking System Health")
        
        agents = [
            ("Client Agent", f"{self.base_url}/health"),
            ("Task Generation Agent", "http://localhost:8001/health"),
            ("Scheduling Agent", "http://localhost:8002/health")
        ]
        
        all_healthy = True
        
        for agent_name, health_url in agents:
            try:
                response = await self.client.get(health_url)
                if response.status_code == 200:
                    print(f"✅ {agent_name}: Healthy")
                else:
                    print(f"❌ {agent_name}: Unhealthy (HTTP {response.status_code})")
                    all_healthy = False
            except Exception as e:
                print(f"❌ {agent_name}: Error - {str(e)}")
                all_healthy = False
        
        if not all_healthy:
            print("\n⚠️  Some agents are not healthy. Please start all agents first:")
            print("   ./scripts/deploy_agents.sh start")
            return False
        
        print("\n🎉 All agents are healthy and ready!")
        return True
    
    async def demonstrate_full_workflow(self):
        """Demonstrate the full workflow (task generation + scheduling)."""
        self.print_step("Demonstrating Full Workflow")
        
        request_data = {
            "project_description": "Build a modern e-commerce platform with user authentication, product catalog, shopping cart, payment processing, and admin dashboard",
            "request_type": "full",
            "requirements": [
                "User authentication and authorization",
                "Product catalog with search and filtering",
                "Shopping cart functionality",
                "Payment gateway integration",
                "Order management system",
                "Admin dashboard",
                "Responsive web design",
                "Security best practices",
                "Performance optimization"
            ],
            "constraints": {
                "budget": "high",
                "timeline": "normal",
                "team_size": 5,
                "technology_stack": "modern"
            },
            "deadline": "2024-12-31"
        }
        
        print("📝 Project Request:")
        print(f"   Description: {request_data['project_description']}")
        print(f"   Type: {request_data['request_type'].upper()}")
        print(f"   Requirements: {len(request_data['requirements'])} items")
        print(f"   Constraints: {len(request_data['constraints'])} items")
        
        print("\n🚀 Sending request to multi-agent system...")
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                f"{self.base_url}/process-project",
                json=request_data
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Request completed in {duration:.2f} seconds")
                print(f"\n📊 Workflow Executed: {' → '.join(result['workflow_executed'])}")
                
                # Display task generation results
                if 'task_generation' in result['results']:
                    task_gen = result['results']['task_generation']
                    tasks = task_gen.get('tasks', [])
                    
                    print(f"\n📝 Task Generation Results:")
                    print(f"   • Project ID: {task_gen.get('project_id')}")
                    print(f"   • Generated Tasks: {len(tasks)}")
                    print(f"   • Total Duration: {task_gen.get('total_estimated_duration')}")
                    
                    print(f"\n   Top 5 Tasks:")
                    for i, task in enumerate(tasks[:5], 1):
                        priority_icon = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(task.get('priority', 'medium'), "⚪")
                        print(f"   {i}. {priority_icon} {task.get('title')} ({task.get('estimated_duration')})")
                
                # Display scheduling results
                if 'scheduling' in result['results']:
                    scheduling = result['results']['scheduling']
                    schedule = scheduling.get('schedule', [])
                    
                    print(f"\n📅 Scheduling Results:")
                    print(f"   • Schedule Items: {len(schedule)}")
                    print(f"   • Total Duration: {scheduling.get('total_duration')}")
                    print(f"   • Critical Path: {len(scheduling.get('critical_path', []))} tasks")
                    print(f"   • Timeline: {scheduling.get('timeline_summary', 'No timeline')}")
                
                return True
                
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Request failed: {str(e)}")
            return False
    
    async def demonstrate_partial_workflow(self):
        """Demonstrate the partial workflow (task generation only)."""
        self.print_step("Demonstrating Partial Workflow")
        
        request_data = {
            "project_description": "Create a mobile application for personal finance management with expense tracking, budget planning, and financial insights",
            "request_type": "partial",
            "requirements": [
                "Expense tracking and categorization",
                "Budget planning and monitoring",
                "Financial insights and analytics",
                "Data synchronization across devices",
                "Offline functionality",
                "Security and privacy protection"
            ],
            "constraints": {
                "budget": "medium",
                "platform": "mobile",
                "target_audience": "individuals"
            }
        }
        
        print("📝 Project Request:")
        print(f"   Description: {request_data['project_description']}")
        print(f"   Type: {request_data['request_type'].upper()}")
        print(f"   Requirements: {len(request_data['requirements'])} items")
        
        print("\n🚀 Sending request to multi-agent system...")
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                f"{self.base_url}/process-project",
                json=request_data
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Request completed in {duration:.2f} seconds")
                print(f"\n📊 Workflow Executed: {' → '.join(result['workflow_executed'])}")
                
                # Display task generation results
                if 'task_generation' in result['results']:
                    task_gen = result['results']['task_generation']
                    tasks = task_gen.get('tasks', [])
                    
                    print(f"\n📝 Task Generation Results:")
                    print(f"   • Project ID: {task_gen.get('project_id')}")
                    print(f"   • Generated Tasks: {len(tasks)}")
                    print(f"   • Total Duration: {task_gen.get('total_estimated_duration')}")
                    
                    print(f"\n   All Tasks:")
                    for i, task in enumerate(tasks, 1):
                        priority_icon = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(task.get('priority', 'medium'), "⚪")
                        print(f"   {i}. {priority_icon} {task.get('title')} ({task.get('estimated_duration')})")
                
                # Verify no scheduling results
                if 'scheduling' not in result['results']:
                    print(f"\n✅ Confirmed: No scheduling performed (partial request)")
                
                return True
                
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Request failed: {str(e)}")
            return False
    
    async def demonstrate_agent_communication(self):
        """Demonstrate direct agent communication."""
        self.print_step("Demonstrating Agent Communication")
        
        # Get agent list
        try:
            response = await self.client.get(f"{self.base_url}/agents/list")
            if response.status_code == 200:
                data = response.json()
                agents = data.get('agents', [])
                
                print(f"📡 Available Agents: {len(agents)}")
                for agent in agents:
                    status_icon = "✅" if agent.get('status') == 'active' else "❌"
                    print(f"   {status_icon} {agent.get('name', 'Unknown')}")
                    print(f"      URL: {agent.get('url')}")
                    print(f"      Framework: {agent.get('framework')}")
                    print(f"      Capabilities: {', '.join(agent.get('capabilities', []))}")
                    print()
                
                return True
            else:
                print(f"❌ Failed to get agent list: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to communicate with agents: {str(e)}")
            return False
    
    async def demonstrate_error_handling(self):
        """Demonstrate error handling capabilities."""
        self.print_step("Demonstrating Error Handling")
        
        # Test invalid request type
        invalid_request = {
            "project_description": "Test project",
            "request_type": "invalid_type"
        }
        
        print("🧪 Testing invalid request type...")
        
        try:
            response = await self.client.post(
                f"{self.base_url}/process-project",
                json=invalid_request
            )
            
            if response.status_code == 400:
                print("✅ Invalid request properly rejected with HTTP 400")
                error_data = response.json()
                print(f"   Error message: {error_data.get('detail', 'No detail')}")
                return True
            else:
                print(f"❌ Expected HTTP 400, got {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error handling test failed: {str(e)}")
            return False
    
    async def run_complete_demo(self):
        """Run the complete demonstration."""
        self.print_header("Multi-Agent System Demonstration")
        
        print("🎯 This demonstration will showcase:")
        print("   • System health checks")
        print("   • Full workflow (task generation + scheduling)")
        print("   • Partial workflow (task generation only)")
        print("   • Agent communication")
        print("   • Error handling")
        
        input("\nPress Enter to start the demonstration...")
        
        # Run all demonstration steps
        steps = [
            ("System Health Check", self.check_system_health),
            ("Full Workflow", self.demonstrate_full_workflow),
            ("Partial Workflow", self.demonstrate_partial_workflow),
            ("Agent Communication", self.demonstrate_agent_communication),
            ("Error Handling", self.demonstrate_error_handling)
        ]
        
        passed = 0
        total = len(steps)
        
        for step_name, step_func in steps:
            if await step_func():
                passed += 1
            
            if step_name != steps[-1][0]:  # Not the last step
                input(f"\nPress Enter to continue to next demonstration...")
        
        # Final summary
        self.print_header("Demonstration Complete")
        
        print(f"📊 Results: {passed}/{total} demonstrations successful")
        
        if passed == total:
            print("🎉 All demonstrations completed successfully!")
            print("\n✨ The multi-agent system is working perfectly!")
        else:
            print(f"⚠️  {total - passed} demonstrations had issues")
            print("   Please check the system logs for more details")
        
        print("\n📚 Next Steps:")
        print("   • Explore examples.py for more usage patterns")
        print("   • Run test_system.py for comprehensive testing")
        print("   • Check logs/ directory for detailed system logs")
        print("   • Read README.md for complete documentation")
    
    async def close(self):
        """Close the demo client."""
        await self.client.aclose()

async def main():
    """Main demo function."""
    demo = MultiAgentDemo()
    
    try:
        await demo.run_complete_demo()
    finally:
        await demo.close()

if __name__ == "__main__":
    asyncio.run(main())
