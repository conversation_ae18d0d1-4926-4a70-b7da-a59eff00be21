"""Configuration settings for the multi-agent system."""

import os
from typing import Dict, Any
from pydantic import BaseSettings

class Settings(BaseSettings):
    """Application settings."""
    
    # Agent ports
    CLIENT_AGENT_PORT: int = 8000
    TASK_GENERATION_AGENT_PORT: int = 8001
    SCHEDULING_AGENT_PORT: int = 8002
    
    # Agent URLs
    TASK_GENERATION_AGENT_URL: str = f"http://localhost:{TASK_GENERATION_AGENT_PORT}"
    SCHEDULING_AGENT_URL: str = f"http://localhost:{SCHEDULING_AGENT_PORT}"
    
    # OpenRouter API
    OPENROUTER_API_KEY: str = os.getenv("OPENROUTER_API_KEY", "")
    OPENROUTER_BASE_URL: str = "https://openrouter.ai/api/v1"
    
    # Default model
    DEFAULT_MODEL: str = "anthropic/claude-3-haiku"
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
    
    # ACP Protocol
    ACP_VERSION: str = "1.0"
    ACP_TIMEOUT: int = 30
    
    class Config:
        env_file = ".env"

# Global settings instance
settings = Settings()

# Agent configurations
AGENT_CONFIGS: Dict[str, Dict[str, Any]] = {
    "task_generation": {
        "name": "TaskGenerationAgent",
        "description": "Generates comprehensive task lists from project descriptions",
        "port": settings.TASK_GENERATION_AGENT_PORT,
        "capabilities": ["task_generation", "project_analysis"],
        "framework": "crewai"
    },
    "scheduling": {
        "name": "SchedulingAgent", 
        "description": "Creates time-based scheduling and planning from task lists",
        "port": settings.SCHEDULING_AGENT_PORT,
        "capabilities": ["scheduling", "time_planning", "resource_allocation"],
        "framework": "smolagents"
    },
    "client": {
        "name": "ClientAgent",
        "description": "Routes requests to appropriate specialized agents",
        "port": settings.CLIENT_AGENT_PORT,
        "capabilities": ["routing", "request_handling", "response_aggregation"],
        "framework": "fastacp"
    }
}

# Request type configurations
REQUEST_TYPES = {
    "full": {
        "description": "Complete workflow: task generation + scheduling",
        "agents": ["task_generation", "scheduling"],
        "workflow": ["task_generation", "scheduling"]
    },
    "partial": {
        "description": "Partial workflow: task generation only",
        "agents": ["task_generation"],
        "workflow": ["task_generation"]
    }
}
