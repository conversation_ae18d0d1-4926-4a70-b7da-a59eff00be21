"""Base agent class for all specialized agents."""

import asyncio
import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, List
from fastapi import FastAP<PERSON>, HTTPException
from loguru import logger
import uvicorn

from acp.protocol import ACPProtocol
from acp.message_types import AgentIn<PERSON>, AgentCapability
from logging_config import HTTPLoggingMiddleware, setup_agent_logging

class BaseAgent(ABC):
    """Base class for all agents in the multi-agent system."""
    
    def __init__(
        self, 
        agent_id: str, 
        name: str, 
        description: str, 
        capabilities: List[AgentCapability],
        port: int,
        framework: str = "base"
    ):
        """Initialize the base agent.
        
        Args:
            agent_id: Unique identifier for the agent
            name: Human-readable name
            description: Agent description
            capabilities: List of agent capabilities
            port: Port to run the agent on
            framework: Framework used by the agent
        """
        self.agent_id = agent_id
        self.port = port
        
        # Create agent info
        self.agent_info = AgentInfo(
            agent_id=agent_id,
            name=name,
            description=description,
            capabilities=capabilities,
            endpoint=f"http://localhost:{port}",
            framework=framework
        )
        
        # Initialize ACP protocol
        self.acp = ACPProtocol(agent_id, self.agent_info)
        
        # Setup logging
        setup_agent_logging(agent_id)

        # Create FastAPI app
        self.app = FastAPI(
            title=f"{name} API",
            description=description,
            version="1.0.0"
        )

        # Add HTTP logging middleware
        self.app.add_middleware(HTTPLoggingMiddleware, agent_name=agent_id)

        # Register routes
        self._register_routes()
        
        # Register ACP handlers
        self._register_acp_handlers()
        
        logger.info(f"Base agent initialized: {agent_id} on port {port}")
    
    def _register_routes(self):
        """Register FastAPI routes."""
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {
                "status": "healthy",
                "agent_id": self.agent_id,
                "name": self.agent_info.name,
                "capabilities": self.agent_info.capabilities
            }
        
        @self.app.get("/info")
        async def get_agent_info():
            """Get agent information."""
            return self.acp.get_agent_info()
        
        @self.app.post("/acp/message")
        async def handle_acp_message(message: Dict[str, Any]):
            """Handle incoming ACP messages."""
            logger.info(f"Received ACP message: {message.get('message_id', 'unknown')}")
            logger.debug(f"Message content: {message}")
            
            try:
                response = await self.acp.handle_message(message)
                logger.info(f"Sending ACP response: {response.get('message_id', 'unknown')}")
                logger.debug(f"Response content: {response}")
                return response
            except Exception as e:
                logger.error(f"Error handling ACP message: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
    
    def _register_acp_handlers(self):
        """Register ACP message handlers."""
        
        # Register status handler
        self.acp.register_handler("get_status", self._handle_get_status)
        
        # Register custom handlers (implemented by subclasses)
        self._register_custom_handlers()
    
    async def _handle_get_status(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle get_status requests."""
        return {
            "agent_id": self.agent_id,
            "status": "active",
            "capabilities": self.agent_info.capabilities,
            "framework": self.agent_info.framework,
            "timestamp": str(asyncio.get_event_loop().time())
        }
    
    @abstractmethod
    def _register_custom_handlers(self):
        """Register custom ACP handlers specific to this agent type."""
        pass
    
    @abstractmethod
    async def process_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Process the main request for this agent type.
        
        Args:
            payload: Request payload
            
        Returns:
            Processing result
        """
        pass
    
    def run(self, host: str = "0.0.0.0"):
        """Run the agent server.
        
        Args:
            host: Host to bind to
        """
        logger.info(f"Starting {self.agent_info.name} on {host}:{self.port}")
        
        uvicorn.run(
            self.app,
            host=host,
            port=self.port,
            log_level="info"
        )
    
    async def close(self):
        """Close the agent and cleanup resources."""
        await self.acp.close()
        logger.info(f"Agent {self.agent_id} closed")
    
    def generate_id(self) -> str:
        """Generate a unique ID."""
        return str(uuid.uuid4())
    
    def log_request(self, action: str, payload: Dict[str, Any]):
        """Log incoming request."""
        logger.info(f"[{self.agent_id}] Processing {action} request")
        logger.debug(f"[{self.agent_id}] Request payload: {payload}")
    
    def log_response(self, action: str, result: Dict[str, Any]):
        """Log outgoing response."""
        logger.info(f"[{self.agent_id}] Completed {action} request")
        logger.debug(f"[{self.agent_id}] Response result: {result}")
